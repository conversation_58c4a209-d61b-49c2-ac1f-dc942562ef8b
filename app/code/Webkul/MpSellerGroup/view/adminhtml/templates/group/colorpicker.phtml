<?php
/**
 * Webkul Software.
 *
 * @category  Webkul
 * @package   Webkul_MpSellerGroup
 * <AUTHOR>
 * @copyright Copyright (c) Webkul Software Private Limited (https://webkul.com)
 * @license   https://store.webkul.com/license.html
 */
?>
<div class="admin__field" data-index="group_color1">
    <div class="admin__field-control" style="background-color:<?= $block->escapeHtml($block->getGroupColor())?>">
        <input class="admin__control-text" type="text" name="mpsellergroup_group[group_color]"
               data-form-part="mpsellergroup_group_form"  value="<?= $block->escapeHtml($block->getGroupColor())?>"
               style="background-color:<?= $block->escapeHtml($block->getGroupColor())?>">
    </div>
    <span><?= $block->escapeHtml(__("Click on the field to set color for Group")) ?></span>
</div>
<script type="text/javascript">
    require([
        "jquery",
        "jquery/colorpicker/js/colorpicker"
    ], function ($) {
        var self = this;
        var thisElement = '[name="mpsellergroup_group[group_color]"]';
        $(thisElement).css(
            'background-color',''
        );
        $(thisElement).ColorPicker({
            color: "",
            onShow: function (colorPicker) {
                $(colorPicker).fadeIn(500);
                return false;
            },
            onHide: function (colorPicker) {
                $(colorPicker).fadeOut(500);
                return false;
            },
            onChange: function (hsb, hex, rgb) {
                $(thisElement).parent().attr('style','');
                $(thisElement).val('#' + hex);
                $(thisElement).css('background-color','#'+hex);
            }
        });
    });
</script>