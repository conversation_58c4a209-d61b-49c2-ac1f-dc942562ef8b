<?php
/**
 * Webkul Software.
 *
 * @category  Webkul
 * @package   Webkul_Marketplace
 * <AUTHOR>
 * @copyright Copyright (c) Webkul Software Private Limited (https://webkul.com)
 * @license   https://store.webkul.com/license.html
 */


$viewModel = $block->getViewModel();
$helper = $viewModel->getHelper();
$orderHelper = $viewModel->getOrderHelper();
$orderId = $block->getRequest()->getParam('id');
$order = $block->getOrder();
$state = $order->getState();
$status = $order->getStatus();

$store = $order->getStore();
$website = $store->getWebsite();
$websiteName = $website->getName();

$orderComments = $order->getAllStatusHistory();
$statuses = $this->getOrder()->getConfig()->getStateStatuses($state);
$customStatus = "";
    if ($status == 'pre_transit' && $state == 'complete'){
        $customStatus = "Processing";
    }elseif ($status == 'in_transit' && $state == 'complete') {
        $customStatus = "Processing";
   }elseif ($status == 'complete ' && $state == 'complete'){
        $customStatus = "Shipped";
   }elseif ($status == 'delivered ' && $state == 'complete'){
        $customStatus = "Delivered";
   }

$orderStatusLabel = $order->getStatusLabel();
$paymentCode = '';
$paymentMethod = '';
if ($order->getPayment()) {
    $paymentCode = $order->getPayment()->getMethod();
    $paymentMethod = $order->getPayment()->getMethodInstance()->getTitle();
}

$marketplaceOrders = $block->getSellerOrderInfo($orderId);
$orderNumber = $order->getRealOrderId();
if ($marketplaceOrders->getSize()) {
    $orderNumber = $marketplaceOrders->getFirstItem()->getSellerOrderNumber() ?:
        $orderNumber;
    $adminPayStatus = $block->getAdminPayStatus($orderId);
    $itemCollection = $block->getOrderItemCollection($orderId);
    $qtyToInvoiceAvail = $block->getQtyToInvoiceCount($itemCollection);
    $qtyToShipAvail = $block->getQtyToShipCount($itemCollection);
    $qtyToCancelAvail = $block->getQtyToCancelCount($itemCollection);
    //$qtyToRefundAvail = $block->getQtyToRefundCollection($orderId);
    $qtyToRefundAvail = $block->getQtyToRefundCount($itemCollection);

    $tracking = $orderHelper->getOrderinfo($orderId);
    if ($tracking != "") {
        $disabled = $tracking->getTrackingNumber()==''? "":"readonly='readonly'";
        $shipmentId = $tracking->getShipmentId();
        $invoiceId=$tracking->getInvoiceId();
        $creditmemoId=$tracking->getCreditmemoId();

        $isCanceled=$tracking->getIsCanceled();
        if ($isCanceled && !$qtyToCancelAvail) {
            $orderStatusLabel = 'Canceled';
        }

        if ($shipmentId) {
            $itemShipStatus = 'Shipped';
        } else {
            $itemShipStatus = '';
        }
        if ($invoiceId) {
            $itemInvoiceStatus = 'Invoiced';
        } else {
            $itemInvoiceStatus = '';
        }
        if ($creditmemoId && !$qtyToRefundAvail) {
            $shippingamount=$tracking->getShippingCharges();
            $refundedShippingAmount=$tracking->getRefundedShippingCharges();
            if ($shippingamount-$refundedShippingAmount == 0) {
                $itemRefundStatus = 'Refunded';
            } else {
                $itemRefundStatus = '';
            }
        } else {
            $itemRefundStatus = '';
        }

        $itemCancelStatus = $isCanceled;

        $invoiceId = $tracking->getInvoiceId();
    }
    /** @var $viewModelCustomerEmail \Comave\MaskedEmail\ViewModel\CustomerEmail */
    $viewModelCustomerEmail = $block->getData('view_model_customer_email');
    ?>
    <div class="wk-mp-design">
        <div class="fieldset wk-mp-fieldset">
            <div class="page-title-wrapper">
                <h1 class="page-title">
                    <span data-ui-id="page-title-wrapper" class="base">
                        <?= /* @noEscape */ __('Manage Order #%1', $orderNumber) ?>
                    </span>
                </h1>
                <span class="order-status <?= $escaper->escapeHtml(strtolower($state)); ?>">
                    <?php if(!empty($customStatus)){
                        echo $customStatus;
                    }else{
                        echo $status;
                    }?>
                </span>
                <div class="order-date">
                    <?= /* @noEscape */ __('<span class="label">Order Date:</span> %1', '<date>' . $block
                    ->formatDate($order->getCreatedAt(), \IntlDateFormatter::MEDIUM, true, $block
                    ->getTimezoneForStore($order->getStore())) . '</date>') ?>
                </div>
                <div class="order-approval-section">
                    <div class="order-section">
                        <?php if($order->getData('order_approval_enabled')){?>
                            <div clas="delay-time">
                                <div class="btn-group">
                                    <button class="order_delay_time" value="+15min">+15min</button>
                                    <button class="order_delay_time" value="+30min">+30min</button>
                                    <button class="order_delay_time" value="+45min">+45min</button>
                                    <button class="order_delay_time" value="+1hr">+1hr</button>
                                    <button class="order_delay_time" value="+1hr15min">+1hr15min</button>
                                    <button class="order_delay_time" value="+1hr30min">+1hr30min</button>
                                    <button class="order_delay_time" value="+1hr45min">+1hr45min</button>
                                    <button class="order_delay_time" value="+2hr">+2hr</button>
                                </div>
                            </div>
                            <div class="field comment">
                                <label class="label" for="custom_comment"><?php echo __('Message to customer:') ?></label>
                                <div class="control">
                                    <textarea name="custom_comment" id="custom_comment" cols="30" rows="5"></textarea>
                                </div>
                                </div>
                                <div class="actions-toolbar order-actions-toolbar">
                                    <div class="actions">
                                        <a href="#" class="action accept-order" id="accept-order">
                                            <button class="button wk-mp-btn"
                                                title="<?= /* @noEscape */ __('Accept Order') ?>" type="button">
                                                <span><span><?= /* @noEscape */ __('ACCEPT') ?></span></span>
                                            </button>
                                        </a>

                                        <a href="#" class="action reject-order" id="reject-order">
                                            <button class="button wk-mp-btn"
                                                title="<?= /* @noEscape */ __('Reject Order') ?>" type="button">
                                                <span><span><?= /* @noEscape */ __('REJECT') ?></span></span>
                                            </button>
                                        </a>
                                    </div>
                                </div>
                       <?php } ?>
                    </div>
                </div>
                <div class="actions-toolbar order-actions-toolbar">
                    <div class="actions">
                        <a onclick="this.target='_blank';" href="<?= $escaper->escapeUrl($block
                        ->getUrl('marketplace/order/printorder', ['id'=>$orderId, '_secure' => $block
                        ->getRequest()->isSecure()]));?>" class="action">
                            <button class="button wk-mp-btn " title="<?= /* @noEscape */ __('Print') ?>"
                             type="submit" id="save_butn" >
                                <span><span><?= /* @noEscape */ __('Print') ?></span></span>
                            </button>
                        </a>
                        <?php if ($itemCancelStatus!="1" && $order->canCancel()) {?>
                            <a href="<?= $escaper->escapeUrl($block
                            ->getUrl('marketplace/order/cancel', ['id'=>$orderId, '_secure' => $block
                            ->getRequest()->isSecure()]))?>" onclick="return confirm('<?= /* @noEscape */
                            __("Are you sure you want to cancel this order?") ?>')" class="action">
                                <button class="button wk-mp-btn" title="<?= /* @noEscape */ __('Cancel Order') ?>"
                                 type="button">
                                    <span><span><?= /* @noEscape */ __('Cancel Order') ?></span></span>
                                </button>
                            </a>
                            <?php
                        } ?>
                        <?php if (($itemCancelStatus!="1" && !$order->isCanceled()) || $qtyToCancelAvail) { ?>
                            <a href="<?= $escaper->escapeUrl($block
                            ->getUrl('marketplace/order/email', ['id'=>$orderId, '_secure' => $block
                            ->getRequest()->isSecure()]))?>" onclick="return confirm('<?= /* @noEscape */
                             __("Are you sure you want to send order email to customer?") ?>')" class="action">
                                <button class="button wk-mp-btn"
                                title="<?= /* @noEscape */ __('Send Email To Customer') ?>"
                                 type="button">
                                    <span><span><?= /* @noEscape */ __('Send Email') ?></span></span>
                                </button>
                            </a>
                            <?php
                        } ?>
                        <?php if ($qtyToInvoiceAvail && $order->canInvoice()) {?>
                            <a href="<?= $escaper->escapeUrl($block
                            ->getUrl('marketplace/order_invoice/create', ['id'=>$orderId, '_secure' => $block
                            ->getRequest()->isSecure()]))?>" class="action">
                                <button class="button wk-mp-btn"
                                title="<?= /* @noEscape */
                                 __('Create Invoice to confirm collected amount from buyer for this order') ?>"
                                 onclick="return confirm('<?= /* @noEscape */
                                    __("Are you sure you want to create invoice?") ?>')" type="button">
                                    <span><span><?= /* @noEscape */ __('Invoice') ?></span></span>
                                </button>
                            </a>
                            <?php
                        }
                        if ($itemInvoiceStatus=="Invoiced" && $itemRefundStatus!="Refunded"
                         && $order->canCreditmemo()) {
                            if ($paymentCode == 'mpcashondelivery' && !$adminPayStatus) { ?>
                                <a href="<?= $escaper->escapeUrl($block
                                ->getUrl('mpcashondelivery/order/payadmin', ['id'=>$orderId, '_secure' => $block
                                ->getRequest()->isSecure()]))?>" class="action">
                                    <button class="button wk-mp-btn" title="<?= /* @noEscape */
                                     __('Pay Admin for his commission for this order') ?>"
                                     onclick="return confirm('<?= /* @noEscape */ __("If you pay admin
                                     commission then you can not do refund for buyer in future.
                                     Are you sure you want to pay admin for his commission?") ?>')" type="button">
                                        <span><span><?= /* @noEscape */ __('Pay Admin Commission') ?></span></span>
                                    </button>
                                </a>
                                <a href="<?= $escaper->escapeUrl($block
                                ->getUrl('marketplace/order_creditmemo/create', ['order_id'=>$orderId, '_secure'=>$block
                                ->getRequest()->isSecure()]))?>" class="action">
                                    <button class="button wk-mp-btn"
                                    title="<?= /* @noEscape */ __('Create Credit Memo') ?>"
                                     type="button">
                                        <span><span><?= /* @noEscape */ __('Credit Memo') ?></span></span>
                                    </button>
                                </a>
                                <?php
                            } elseif ($paymentCode != 'mpcashondelivery') { ?>
                                <a href="<?= $escaper->escapeUrl($block
                                ->getUrl('marketplace/order_creditmemo/create', ['order_id'=>$orderId, '_secure'=>$block
                                ->getRequest()->isSecure()]))?>" class="action">
                                    <button class="button wk-mp-btn"
                                    title="<?= /* @noEscape */ __('Create Credit Memo') ?>" type="button">
                                        <span><span><?= /* @noEscape */ __('Credit Memo') ?></span></span>
                                    </button>
                                </a>
                                <?php
                            }
                        } ?>
                        <?php
                        if ($qtyToShipAvail && $block->isOrderCanShip($order)) { ?>
                            <a href="<?= $escaper->escapeUrl($block
                                ->getUrl('marketplace/order_shipment/create', ['id'=>$orderId, '_secure' => $block
                                ->getRequest()->isSecure()]))?>" class="action">
                                <button class="button wk-mp-btn"
                                title="<?= /* @noEscape */ __('Create Shipment for Order') ?>"
                                type="button" id="mp-create-shipment-btn">
                                    <span><span><?= /* @noEscape */ __('Ship') ?></span></span>
                                </button>
                            </a>
                            <?php
                        } ?>
                    </div>
                </div>
            </div>
            <?php
            $_links = $block->getLinks();
            ?>
            <ul class="items order-links">
                <?php foreach ($_links as $_link): ?>
                    <?php if (strpos($_link['url'], "marketplace/order/view") === false): ?>
                        <li class="nav item">
                            <a href="<?= $escaper->escapeUrl($_link['url']) ?>">
                                <?= $escaper->escapeHtml($_link['label']) ?>
                            </a>
                        </li>
                    <?php else: ?>
                        <li class="nav item current"><strong><?= $escaper->escapeHtml($_link['label']) ?></strong></li>
                    <?php endif; ?>
                <?php endforeach; ?>
            </ul>
            <div class="order-details-items ordered">
                <div class="order-title">
                    <strong><?= /* @noEscape */ __('Items Ordered') ?></strong>
                </div>
                <?= $block->getChildHtml('marketplace_order_items') ?>
            </div>
            <form id="custom_order_form" action="<?= $escaper->escapeUrl($block
                                ->getUrl('marketplace/order/manageorder', ['_secure' => $block
                                ->getRequest()->isSecure()]))?>" method="post">


                <label for="custom_order_status">Select Order Status:</label>

                <select id="custom_order_status" name="custom_order_status">
                    <?php foreach ($statuses as $_code => $_label): ?>

                        <option value="<?= $block->escapeHtmlAttr($_code) ?>" <?php if ($_code == $status): ?> selected="selected" <?php endif; ?>>
                            <?= $block->escapeHtml($_label) ?>
                        </option>
                    <?php endforeach; ?>
                </select>

                <div class="admin__field">
                    <label for="history_comment" class="admin__field-label">
                        <?= $block->escapeHtml(__('Comment')) ?>
                    </label>
                    <div class="admin__field-control">
                        <textarea name="history_comment" rows="3" cols="5" id="history_comment" class="admin__control-textarea"></textarea>
                    </div>
                </div>

                <input type="hidden" name="order_id" value="<?= $orderId ?>" />

                <button type="submit">Save</button>

                <?php if (!empty($orderComments)): ?>
                    <h2><?php echo __('Order Comments') ?></h2>
                    <ul>
                        <?php foreach ($orderComments as $comment): ?>
                            <?php if (!empty($comment->getComment())): ?>
                                <li><?php echo $comment->getComment() ?></li>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    </ul>
                <?php endif; ?>
            </form>


            <?php if ($helper->getSellerProfileDisplayFlag()) { ?>
                <div class="block block-order-details-view">
                    <div class="block-title">
                        <strong><?= /* @noEscape */ __('Buyer Information') ?></strong>
                    </div>
                    <div class="block-content">
                        <div class="box-content">
                            <div class="box">
                                <div class="wk-row">
                                    <span class="label"><?= /* @noEscape */ __('Customer Name')?> : </span>
                                    <span class="value"><?= $escaper->escapeHtml($order->getCustomerName()); ?></span>
                                </div>
                                <div class="wk-row">
                                    <span class="label"><?= /* @noEscape */ __('Email')?> : </span>
                                    <span class="value">
                                        <?= ($viewModelCustomerEmail)?
                                            $viewModelCustomerEmail->getCustomerEmail(
                                                (int)$order->getCustomerId(), $order->getCustomerEmail()
                                            )
                                            : $order->getCustomerEmail()
                                        ?>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php } ?>
            <div class="block block-order-details-view">
                <div class="block-title">
                    <strong><?= /* @noEscape */ __('Order Information') ?></strong>
                </div>
                <div class="block-content">
                    <?php if ($block->isOrderCanShip($order)): ?>
                        <?php if ($helper->getSellerProfileDisplayFlag()) { ?>
                            <div class="box box-order-shipping-address">
                                <strong class="box-title"><span>
                                    <?= /* @noEscape */ __('Shipping Address') ?></span></strong>
                                <div class="box-content">
                                    <address><?= /* @noEscape */ $block->getFormattedAddress($order
                                    ->getShippingAddress()); ?></address>
                                </div>
                            </div>
                            <?php
                        } ?>
                        <div class="box box-order-shipping-method">
                            <strong class="box-title">
                                <span><?= /* @noEscape */ __('Shipping Method') ?></span>
                            </strong>
                            <div class="box-content">
                            <?php if ($order->getShippingDescription()): ?>
                                <?= $escaper->escapeHtml($order->getShippingDescription()) ?>
                            <?php else: ?>
                                <?= /* @noEscape */ __('No shipping information available'); ?>
                            <?php endif; ?>
                            </div>
                            <?php
                            if ($itemShipStatus!="Shipped" && $itemCancelStatus!="1"
                             && $block->isOrderCanShip($order)) { ?>
                                <form action="<?= $escaper->escapeUrl($block
                                ->getUrl('marketplace/order/shipment', ['_secure' => $block
                                ->getRequest()->isSecure()]))?>" id="marketplace-shipping-form" method="post">
                                    <input class="required-entry" type="hidden"
                                    value="<?= $escaper->escapeHtml($order->getId()); ?>" name="id"/>
                                    <table class="data-table orders wk-mp-list_table" id="my-orders-table">
                                        <col width="1" />
                                        <col width="1" />
                                        <tbody>
                                            <?php
                                            foreach ($block->getChildNames() as $key => $blockName) {
                                                if ($blockName != 'marketplace_order_items') {
                                                    /* @noEscape */ echo $block->getChildHtml($blockName, false);
                                                }
                                            } ?>
                                            <tr>
                                                <td>
                                                    <input class="carrier input-text"
                                                     value="<?= $escaper->escapeHtml($tracking->getCarrierName()); ?>"
                                                      <?= /* @noEscape */ $disabled;?> type="text" name="carrier"
                                                       placeholder="<?= /* @noEscape */ __('Carrier') ?>"/>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>
                                                    <input class="required-entry input-text"
                                                     value="<?= $escaper->escapeHtml($tracking->getTrackingNumber());?>"
                                                      <?= /* @noEscape */ $disabled;?> type="text" name="tracking_id"
                                                       placeholder="<?= /* @noEscape */ __('Tracking Number') ?>"/>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </form>
                                <?php
                            } ?>
                        </div>
                    <?php endif; ?>
                    <?php if ($helper->getSellerProfileDisplayFlag()) { ?>
                        <div class="box box-order-billing-address">
                            <strong class="box-title">
                                <span><?= /* @noEscape */ __('Billing Address') ?></span>
                            </strong>
                            <div class="box-content">
                                <address><?= /* @noEscape */ $block->getFormattedAddress($order->getBillingAddress());?>
                            </address>
                            </div>
                        </div>
                        <?php
                    } ?>
                    <div class="box box-order-billing-method">
                        <strong class="box-title">
                            <span><?= /* @noEscape */ __('Payment Method') ?></span>
                        </strong>
                        <div class="box-content">
                            <?= $escaper->escapeHtml($paymentMethod); ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="buttons-set">
        <p class="back-link">
            <a href="<?= $escaper->escapeUrl($block
            ->getUrl('marketplace/order/history', ['_secure' => $block
            ->getRequest()->isSecure()]));?>"
            class="left">&laquo; <?= /* @noEscape */ __('Back To My Orders') ?></a>
        </p>
    </div>
    <?php
}?>
<script>
    require(['jquery'], function($) {
        $(document).ready(function() {
            var order_delay_time;
            $('.order_delay_time').click(function(){
                order_delay_time = $(this).val();
            });
            $('#accept-order').click(function(e) {
                e.preventDefault(); // Prevent the default link action

                // Get the order ID and comment value
                var orderId = '<?php echo $orderId; ?>'; // Assuming you have orderId variable defined

                var comment = $('#custom_comment').val();
                // Make AJAX request
                $.ajax({
                    url: '<?php echo $block->getUrl('orderaction/order/accept'); ?>',
                    type: 'POST',
                    dataType: 'json',
                    data: {
                        id: orderId,
                        comment: comment,
                        orderdelaytime: order_delay_time
                    },
                    success: function(response) {
                        if (response.redirectUrl) {
                            window.location.href = response.redirectUrl; // Redirect to the specified URL
                        }
                    },
                    error: function(xhr, status, error) {
                        // Handle error response
                        console.error(xhr.responseText);
                    }
                });
            });
        });
    });
</script>
<script>
    require(['jquery'], function($) {
        $(document).ready(function() {
            $('#reject-order').click(function(e) {
                e.preventDefault(); // Prevent the default link action

                // Get the order ID and comment value
                var orderId = '<?php echo $orderId; ?>'; // Assuming you have orderId variable defined

                var comment = $('#custom_comment').val();
                // Make AJAX request
                $.ajax({
                    url: '<?php echo $block->getUrl('orderaction/order/reject'); ?>',
                    type: 'POST',
                    dataType: 'json',
                    data: {
                        id: orderId,
                        comment: comment
                    },
                    success: function(response) {
                        if (response.redirectUrl) {
                            window.location.href = response.redirectUrl; // Redirect to the specified URL
                        }
                    },
                    error: function(xhr, status, error) {
                        // Handle error response
                        console.error(xhr.responseText);
                    }
                });
            });
        });
    });
</script>

