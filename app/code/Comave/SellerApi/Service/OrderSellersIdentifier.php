<?php

declare(strict_types=1);

namespace Comave\SellerApi\Service;

use Comave\SellerApi\Exception\UnknownIntegrationException;
use Comave\SellerApi\Model\IntegrationTypePool;
use Magento\Sales\Api\Data\OrderInterface;
use Magento\Sales\Api\Data\OrderItemInterface;

class OrderSellersIdentifier
{
    public function __construct(
        private readonly IntegrationTypePool $integrationTypePool
    ) {
    }

    /**
     * @param OrderInterface $order
     * @return array
     */
    public function get(OrderInterface $order): array
    {
        $identifiedItems = [];
        /** @var OrderItemInterface $item */
        foreach ($order->getAllItems() as $item) {
            try {
                if (
                    isset($sellerIdentifier) &&
                    $item->getParentItemId() &&
                    in_array($item->getParentItemId(), $identifiedItems[$sellerIdentifier])
                ) {
                    $identifiedItems[$sellerIdentifier][] = $item->getItemId();
                    continue;
                } else {
                    $integration = $this->integrationTypePool->identifyIntegration(
                        (string) $item->getProductId()
                    );
                    $sellerIdentifier = $integration->getIntegrationType();
                }

            } catch (UnknownIntegrationException) {
                continue;
            }

            if (!isset($identifiedItems[$sellerIdentifier])) {
                $identifiedItems[$sellerIdentifier] = [];
            }

            $identifiedItems[$sellerIdentifier][] = $item->getItemId();
        }

        return $identifiedItems;
    }
}
