<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <virtualType name="SplitOrderLogger" type="Comave\Logger\Model\ComaveLogger">
        <arguments>
            <argument name="name" xsi:type="string">SplitOrderLogger</argument>
            <argument name="loggerPath" xsi:type="string">split_order</argument>
        </arguments>
    </virtualType>

    <type name="Comave\SplitOrder\Model\Queue\Consumer\HandleSplitOrder">
        <arguments>
            <argument xsi:type="object" name="logger">SplitOrderLogger</argument>
        </arguments>
    </type>

    <type name="Comave\SplitOrder\Observer\PublishSplit">
        <arguments>
            <argument xsi:type="object" name="logger">SplitOrderLogger</argument>
        </arguments>
    </type>

    <type name="Comave\SplitOrder\ViewModel\UserInfo">
        <arguments>
            <argument xsi:type="object" name="logger">SplitOrderLogger</argument>
        </arguments>
    </type>

    <type name="Magento\Quote\Model\Quote">
        <plugin name="preventMultipleSellerProducts" disabled="true"/>
    </type>

    <type name="Magento\Quote\Model\Quote\Item\ToOrderItem">
        <plugin name="bySellerOptionAppend" type="Comave\SplitOrder\Plugin\QuoteItemToOrderItemPlugin"/>
    </type>

    <type name="Webkul\Marketplace\Model\ResourceModel\Saleslist">
        <plugin name="addSellerOrderNumber" type="Comave\SplitOrder\Plugin\AppendSellerOrderNumber"/>
    </type>

    <type name="Magento\Customer\CustomerData\Customer">
        <plugin name="isSellerData" type="Comave\SplitOrder\Plugin\CustomerData\SellerFlag"/>
    </type>
</config>
