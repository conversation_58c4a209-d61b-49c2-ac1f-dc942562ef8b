<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <type name="Magento\Sales\Block\Adminhtml\Items\AbstractItems">
        <plugin name="appendSellerOrderInformation" type="Comave\SplitOrder\Plugin\Adminhtml\AppendSellerOrderRow"/>
    </type>

    <type name="Magento\LoginAsCustomerApi\Api\Data\AuthenticationDataInterfaceFactory">
        <plugin name="addMarketplaceOrderReference" type="Comave\SplitOrder\Plugin\Adminhtml\SetMarketplaceOrderIdReference"/>
    </type>

    <type name="Magento\LoginAsCustomerAdminUi\Controller\Adminhtml\Login\Login">
        <arguments>
            <argument xsi:type="object" name="generateAuthenticationSecret">Comave\SplitOrder\Model\Adminhtml\Service\GenerateAuthenticationSecret</argument>
        </arguments>
    </type>
</config>
