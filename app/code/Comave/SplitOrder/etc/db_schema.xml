<?xml version="1.0"?>
<schema xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:Setup/Declaration/Schema/etc/schema.xsd">
    <table name="marketplace_orders">
        <column xsi:type="timestamp" name="created_at" on_update="false" comment="Creation Time" nullable="false" default="CURRENT_TIMESTAMP"/>
        <column xsi:type="timestamp" name="updated_at" on_update="true" comment="Update Time" nullable="true"/>
    </table>

    <table name="marketplace_saleslist">
        <column xsi:type="varchar" name="seller_order_number" comment="Order Number" nullable="true"/>
        <column xsi:type="timestamp" name="created_at" on_update="false" comment="Creation Time" nullable="false" default="CURRENT_TIMESTAMP"/>
        <column xsi:type="timestamp" name="updated_at" on_update="true" comment="Update Time" nullable="true"/>
    </table>
</schema>
