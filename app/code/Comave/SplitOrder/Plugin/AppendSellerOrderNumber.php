<?php

declare(strict_types=1);

namespace Comave\SplitOrder\Plugin;

use Comave\SplitOrder\Model\Command\GetNextIncrementNumber;
use Magento\Framework\Model\AbstractModel;
use Webkul\Marketplace\Model\ResourceModel\Saleslist;

class AppendSellerOrderNumber
{
    public const string STARTING_NUMBER = '100000000';

    /**
     * @param GetNextIncrementNumber $getNextIncrementNumber
     */
    public function __construct(private readonly GetNextIncrementNumber $getNextIncrementNumber)
    {
    }

    /**
     * @param Saleslist $saleslistResource
     * @param AbstractModel $saleslistModel
     * @return array|null
     */
    public function beforeSave(
        Saleslist $saleslistResource,
        AbstractModel $saleslistModel
    ): ?array {
        if ($saleslistModel->getId()) {
            return null;
        }

        $nextNumber = $this->getNextIncrementNumber->get();
        $saleslistModel->setData(
            'seller_order_number',
            $nextNumber
        );

        return [$saleslistModel];
    }
}
