<?php

declare(strict_types=1);

namespace Comave\SplitOrder\Setup\Patch\Data;

use Comave\SplitOrder\Plugin\AppendSellerOrderNumber;
use Magento\Framework\App\ResourceConnection;
use Magento\Framework\Setup\Patch\DataPatchInterface;

class ApplySellerOrderNumber implements DataPatchInterface
{
    /**
     * @param ResourceConnection $resourceConnection
     */
    public function __construct(private readonly ResourceConnection $resourceConnection)
    {
    }

    /**
     * @return string[]
     */
    public static function getDependencies(): array
    {
        return [];
    }

    /**
     * @return string[]
     */
    public function getAliases(): array
    {
        return [];
    }

    /**
     * @return $this
     */
    public function apply(): self
    {
        $connection = $this->resourceConnection->getConnection();
        $connection->update(
            $connection->getTableName('marketplace_saleslist'),
            [
                'seller_order_number' => new \Zend_Db_Expr(
                    sprintf('%s + `entity_id`', AppendSellerOrderNumber::STARTING_NUMBER)
                ),
            ]
        );

        return $this;
    }
}
