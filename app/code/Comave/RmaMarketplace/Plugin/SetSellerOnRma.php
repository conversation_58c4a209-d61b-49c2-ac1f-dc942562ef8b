<?php

declare(strict_types=1);

namespace Comave\RmaMarketplace\Plugin;

use Comave\Rma\Service\SellerReturnAddressProvider;
use Comave\RmaMarketplace\Api\SellerLinkManagementInterface;
use Magento\Customer\Api\CustomerRepositoryInterface;
use Magento\Customer\Api\Data\CustomerInterface;
use Magento\Rma\Api\Data\RmaExtensionInterface;
use Magento\Rma\Api\Data\RmaExtensionInterfaceFactory;
use Magento\Rma\Api\Data\RmaInterface;
use Magento\Sales\Api\OrderRepositoryInterface;

class SetSellerOnRma extends \Comave\Rma\Plugin\SetSellerOnRma
{
    /**
     * @param RmaExtensionInterfaceFactory $extensionFactory
     * @param SellerReturnAddressProvider $sellerReturnAddressProvider
     * @param OrderRepositoryInterface $orderRepository
     * @param CustomerRepositoryInterface $customerRepository
     * @param SellerLinkManagementInterface $sellerLinkManagement
     */
    public function __construct(
        SellerReturnAddressProvider $sellerReturnAddressProvider,
        OrderRepositoryInterface $orderRepository,
        CustomerRepositoryInterface $customerRepository,
        protected RmaExtensionInterfaceFactory $extensionFactory,
        private readonly SellerLinkManagementInterface $sellerLinkManagement,
    ) {
        parent::__construct($extensionFactory, $sellerReturnAddressProvider, $orderRepository, $customerRepository);
    }

    /**
     * @param RmaInterface $rma
     * @param RmaExtensionInterface|null $extension
     * @return RmaExtensionInterface
     */
    public function afterGetExtensionAttributes(
        RmaInterface $rma,
        ?RmaExtensionInterface $extension = null
    ): RmaExtensionInterface {
        $extension ??= $this->extensionFactory->create();

        if ($extension->getSeller()) {
            return $extension;
        }

        try {
            $link = $this->sellerLinkManagement->get($rma);

            if (!$link instanceof CustomerInterface) {
                return $extension;
            }

            $extension->setSeller($link);
            $rma->setExtensionAttributes($extension);

            return $extension;
        } catch (\Exception $e) {
            return parent::afterGetExtensionAttributes($rma, $extension);
        }
    }
}
