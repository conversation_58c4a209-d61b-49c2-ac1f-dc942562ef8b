<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <type name="Magento\Framework\View\Element\UiComponent\DataProvider\CollectionFactory">
        <arguments>
            <argument name="collections" xsi:type="array">
                <item name="seller_rma_list_data_source" xsi:type="string">Comave\RmaMarketplace\Model\ResourceModel\RmaGrid</item>
            </argument>
        </arguments>
    </type>

    <type name="Magento\Rma\Model\ResourceModel\Rma\Status\History">
        <plugin name="setSellerHistoryFlag" type="Comave\RmaMarketplace\Plugin\SetSellerType"/>
    </type>

    <type name="Comave\RmaMarketplace\Model\ResourceModel\RmaGrid">
        <arguments>
            <argument name="mainTable" xsi:type="string">magento_rma_grid</argument>
            <argument name="resourceModel" xsi:type="string">Magento\Rma\Model\ResourceModel\Grid</argument>
        </arguments>
    </type>
</config>
