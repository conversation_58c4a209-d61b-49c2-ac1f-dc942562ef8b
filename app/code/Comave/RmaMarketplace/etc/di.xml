<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <preference for="Comave\RmaMarketplace\Api\SellerLinkManagementInterface" type="Comave\RmaMarketplace\Service\SellerLinkManagement"/>

    <type name="Magento\Rma\Model\ResourceModel\Rma">
        <plugin name="addSellerLink" type="Comave\RmaMarketplace\Plugin\AddSellerLink"/>
    </type>

    <type name="Comave\RmaMarketplace\ViewModel\Rma">
        <arguments>
            <argument xsi:type="object" name="logger">ComaveSellerReturns</argument>
        </arguments>
    </type>

    <type name="Magento\Rma\Api\Data\RmaInterface">
        <plugin name="setSellerInfo" disabled="true"/>
        <plugin name="setSellerInfoByLink" type="Comave\RmaMarketplace\Plugin\SetSellerOnRma"/>
    </type>

    <type name="Magento\Rma\Model\RmaRepository">
        <plugin name="authorization" disabled="true" />
        <plugin name="authorizationSeller" type="Comave\RmaMarketplace\Plugin\Authorization" />
    </type>
</config>
