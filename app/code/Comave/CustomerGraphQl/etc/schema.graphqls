type Mutation {
    confirmCustomer(
        customerId: Int!,
        confirmationToken: String!
    ): <PERSON><PERSON><PERSON>
    @resolver(class: "Comave\\CustomerGraphQl\\Model\\Resolver\\ConfirmCustomer")
    @doc(description: "Confirm the customer account using the confirmation token that the customer received in an email after registering .")

    confirmEmailResend(
        email: String!
    ): <PERSON><PERSON><PERSON>
    @resolver(class: "Comave\\CustomerGraphQl\\Model\\Resolver\\ConfirmEmailResend")
    @doc(description: "Resend customer confirmation email based on the provided email address")

    avatarUploadFile(input: AvatarImageInput!): AvatarSaveOutput @resolver(class: "Comave\\CustomerGraphQl\\Model\\Resolver\\AvatarUpload") @doc(description:"Customer Avatar image Upload")

    referFriend(input: ReferFriendInput!): ReferFriendResponse
    @resolver(class: "Comave\\CustomerGraphQl\\Model\\Resolver\\ReferFriend")
    @doc(description: "Send referral invitation email to provided email address with optional message")
    updateRefundMethod(input: UpdateRefundMethodInput!): UpdateRefundMethodOutput
        @resolver(class: "Comave\\CustomerGraphQl\\Model\\Resolver\\UpdateRefundMethod")
        @doc(description: "Updates the refund method for the authenticated customer")
}

type Query {
    orderInvoicePdfUrl(
        order_id: String! @doc(description: "The base64 encoded order id, as obtained from customer orders items id"),
        invoice_id: String @doc(description: "The base64 encoded invoice id, as obtained from customer orders items invoices id. If missing, first invoice of the order will be returned.")
    ): String
        @doc(description: "Returns url to invoice of the order for unsecured download of the PDF file. Requires authenticated customer of the order for pdf/url generation.")
        @resolver(class: "Comave\\CustomerGraphQl\\Model\\Resolver\\OrderInvoicePdfUrl")
}

type Customer {
    is_confirmed: Boolean @doc(description: "Indicates whether the customer has confirmed email.") @resolver(class: "\\Comave\\CustomerGraphQl\\Model\\Resolver\\IsConfirmed")
    customer_lix_points: Int
        @doc(description: "Indicates the LIX points that the customer has assigned on his account")
        @resolver(class: "Comave\\CustomerGraphQl\\Model\\Resolver\\CustomerLixPoints")
    qr_code: String
        @doc(description: "Returns qr code")
        @resolver(class: "Comave\\CustomerGraphQl\\Model\\Resolver\\CustomerQrCode")
    referFriendList: ReferFriendListResponse
        @doc(description: "Returns list of referred friends")
        @resolver(class: "Comave\\CustomerGraphQl\\Model\\Resolver\\ReferFriendList")
    referralCode: String
        @doc(description: "Returns referral code")
        @resolver(class: "Comave\\CustomerGraphQl\\Model\\Resolver\\ReferralCode")
    avatar: String @doc(description: "Taken from the profile_picture attribute value") @resolver(class: "Comave\\CustomerGraphQl\\Model\\Resolver\\AvatarImage")
    commave_uuid: String @doc(description: "A unique identifier for the customer") @resolver(class: "\\Comave\\CustomerGraphQl\\Model\\Resolver\\CustomerUuid")
    passport: String @doc(description: "Passport for the customer") @resolver(class: "\\Comave\\CustomerGraphQl\\Model\\Resolver\\Passport")
    refund_method: String
        @doc(description: "Returns the selected refund method for the customer account")
        @resolver(class: "Comave\\CustomerGraphQl\\Model\\Resolver\\RefundMethod")
    email_info: String
        @doc(description: "Returns the seller's contact email information.")
        @resolver(class: "Comave\\CustomerGraphQl\\Model\\Resolver\\CustomerEmailInfo")
}

type CustomerOrder {
    lix_points: Float
    @doc(description: "Returns the Lix points for the order.")
    @resolver(class: "\\Comave\\CustomerGraphQl\\Model\\Resolver\\LixPoints")
}

input ReferFriendInput {
    email: String!
    message: String
}

type ReferFriendResponse {
    success: Boolean!
    message: String
}

type ReferFriendListResponse {
    friends: [Friend!]!
}

type Friend {
    email: String
    status: String
    signup_date: String
}

input AvatarImageInput {
    name: String! @doc(description:"The file name include extensions")
    base64_encoded_file: String! @doc(description:"The  base64 code format's file")
    type: String! @doc(description:"Image extension")
}

type AvatarSaveOutput {
    name: String @doc(description: "File Name")
    full_path: String @doc(description: "File Full path")
}

input UpdateRefundMethodInput {
    refund_method: String! @doc(description: "The new refund method to be set for the customer")
}
type UpdateRefundMethodOutput {
    success: Boolean!
    message: String
}
