<?php

declare(strict_types=1);

namespace Comave\CustomerGraphQl\Model\Resolver;

use Comave\CustomerGraphQl\Model\Command\ImageRemover;
use Magento\Customer\Api\CustomerRepositoryInterface;
use Magento\Framework\App\Filesystem\DirectoryList;
use Magento\Framework\Exception\InputException;
use Magento\Framework\Filesystem;
use Magento\Framework\GraphQl\Config\Element\Field;
use Magento\Framework\GraphQl\Exception\GraphQlAuthorizationException;
use Magento\Framework\GraphQl\Exception\GraphQlInputException;
use Magento\Framework\GraphQl\Query\ResolverInterface;
use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;
use Comave\CustomerGraphQl\Model\Service\ImageManager;

class AvatarUpload implements ResolverInterface
{
    /**
     * @param CustomerRepositoryInterface $customerRepository
     * @param ImageManager $imageManager
     * @param ImageRemover $imageRemover
     */
    public function __construct(
        private CustomerRepositoryInterface $customerRepository,
        private ImageManager  $imageManager,
        private ImageRemover $imageRemover
    ) {
    }

    /**
     * @inheritdoc`
     */
    public function resolve(
        Field       $field,
                    $context,
        ResolveInfo $info,
        array       $value = null,
        array       $args = null
    ) {

        $customerId = $context->getUserId();
        if (!$customerId) {
            throw new GraphQlAuthorizationException(__('The current customer isn\'t authorized.'));
        }

        if (empty($args['input']) || !is_array($args['input']) || !count($args['input'])) {
            throw new GraphQlInputException(__('You must specify your input.'));
        }
        if (empty($args['input']['name'])) {
            throw new GraphQlInputException(__('You must specify your "file name".'));
        }

        if (empty($args['input']['base64_encoded_file'])) {
            throw new GraphQlInputException(__('You must specify your "file base64 encode".'));
        }

        try {
            $destinationFolder = 'customer';
            $customer = $this->customerRepository->getById($customerId);
            $profileImage = $customer->getCustomAttribute('profile_picture')?->getValue();

            if ($profileImage !== null) {
                $this->imageRemover->remove($profileImage);
            }

            $arrayReturn = $this->imageManager->processFile($args, $destinationFolder);
            $customer->setCustomAttribute('profile_picture', $arrayReturn['attribute_value']);
            $customer->setData('ignore_validation_flag', true);
            $this->customerRepository->save($customer);

            return $arrayReturn;
        } catch (InputException $e) {
            throw new GraphQlInputException(__($e->getMessage()));
        }
    }
}
