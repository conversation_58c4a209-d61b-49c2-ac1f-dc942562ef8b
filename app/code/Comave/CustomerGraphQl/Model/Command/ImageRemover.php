<?php

declare(strict_types=1);

namespace Comave\CustomerGraphQl\Model\Command;

use Magento\Framework\Filesystem\DirectoryList;
use Magento\Framework\Filesystem\Driver\File;

class ImageRemover
{
    public function __construct(
        private readonly DirectoryList $directoryList,
        private readonly File $fileDriver
    ) {
    }

    /**
     * @param string $profilePicture
     * @return void
     * @throws \Magento\Framework\Exception\FileSystemException
     */
    public function remove(string $profilePicture): void
    {
        $mediaPath = $this->directoryList->getPath(
            \Magento\Framework\App\Filesystem\DirectoryList::MEDIA
        );
        $filePath = sprintf(
            '%s/%s/%s',
            $mediaPath,
            'customer',
            ltrim($profilePicture, '/')
        );

        if ($this->fileDriver->isExists($filePath)) {
            // Delete the file
            $this->fileDriver->deleteFile($filePath);
        }
    }
}
