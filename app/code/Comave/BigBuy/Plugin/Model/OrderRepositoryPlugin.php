<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\BigBuy\Plugin\Model;

use Comave\BigBuy\Model\OrderLinkUiManager;
use Magento\Sales\Api\Data\OrderExtensionFactory;
use Magento\Sales\Api\Data\OrderInterface;
use Magento\Sales\Api\OrderRepositoryInterface;

class OrderRepositoryPlugin
{
    /**
     * @param \Comave\BigBuy\Model\OrderLinkUiManager $orderLinkManager
     * @param \Magento\Sales\Api\Data\OrderExtensionFactory $orderExtensionFactory
     */
    public function __construct(
        private readonly OrderLinkUiManager $orderLinkManager,
        private readonly OrderExtensionFactory $orderExtensionFactory,
    ) {
    }

    /**
     * @param \Magento\Sales\Api\OrderRepositoryInterface $subject
     * @param \Magento\Sales\Api\Data\OrderInterface $result
     * @return \Magento\Sales\Api\Data\OrderInterface
     * @SuppressWarnings(PHPMD.UnusedFormalParameter)
     */
    public function afterGet(OrderRepositoryInterface $subject, OrderInterface $result): OrderInterface
    {
        $extensionAttributes = $result->getExtensionAttributes() ?: $this->orderExtensionFactory->create();
        if (empty($extensionAttributes->getBigbuyOrderLink())) {
            $orderLink = $this->orderLinkManager->getByOrderId((int)$result->getId());
            $extensionAttributes->setBigbuyOrderLink($orderLink);
            $orderLink->setExtensionAttributes($extensionAttributes);
        }

        return $result;
    }
}
