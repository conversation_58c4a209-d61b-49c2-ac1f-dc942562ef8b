<?xml version="1.0" encoding="UTF-8"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <!-- Begin Flat Sync -->
    <virtualType name="Comave\BigBuy\Model\Integration" type="Comave\SellerApi\Model\BaseIntegration">
        <arguments>
            <argument xsi:type="string" name="integrationType">bigbuy</argument>
            <argument xsi:type="string" name="mainProductLinkTable">comave_marketplace_products_custom_api</argument>
            <argument xsi:type="string" name="productColumnIdentifier">product_id</argument>
            <argument xsi:type="string" name="sellerColumnIdentifier">seller_id</argument>
        </arguments>
    </virtualType>
    <type name="Comave\SellerApi\Model\IntegrationTypePool">
        <arguments>
            <argument xsi:type="array" name="integrationPool">
                <item xsi:type="object" name="bigbuy">Comave\BigBuy\Model\Integration</item>
            </argument>
        </arguments>
    </type>
    <!-- End Flat Sync -->
    <!-- Begin Order Synchronise Configurations -->
    <type name="Comave\SellerApi\Model\OrderSynchroniserPoolManager">
        <arguments>
            <argument xsi:type="array" name="configurationPools">
                <item xsi:type="string" name="bigbuy">Comave\BigBuy\Service\Order\SynchroniseService</item>
            </argument>
        </arguments>
    </type>
    <virtualType name="OrderSyncConfigurationPool">
        <arguments>
            <argument xsi:type="array" name="configurationPools">
                <item xsi:type="string" name="bigbuy">Comave\BigBuy\Service\Order\ApiConfiguration</item>
            </argument>
        </arguments>
    </virtualType>
    <type name="Comave\BigBuy\Service\Order\SynchroniseService">
        <arguments>
            <argument xsi:type="object" name="logger">OrderSyncLogger</argument>
        </arguments>
    </type>
    <type name="Comave\BigBuy\Service\Order\Status\Synchroniser">
        <arguments>
            <argument xsi:type="object" name="logger">OrderSyncLogger</argument>
        </arguments>
    </type>
    <type name="Comave\BigBuy\Service\Order\NotificationService">
        <arguments>
            <argument xsi:type="object" name="logger">OrderSyncLogger</argument>
        </arguments>
    </type>
    <type name="Comave\BigBuy\Service\Order\LinkService">
        <arguments>
            <argument xsi:type="object" name="logger">OrderSyncLogger</argument>
        </arguments>
    </type>
    <type name="Comave\BigBuy\Service\Order\TrackingService">
        <arguments>
            <argument xsi:type="object" name="logger">OrderSyncLogger</argument>
        </arguments>
    </type>
    <!-- End Order Synchronise Configurations-->

    <virtualType name="BigBuySetOrderStatusCommand"
                 type="Comave\BigBuy\Console\Command\SetOrderStatus">
        <arguments>
            <argument name="name" xsi:type="string">bigbuy:set:order-status</argument>
        </arguments>
    </virtualType>
    <virtualType name="BigBuySetSellerIntegrationTypeCommand"
                 type="Comave\BigBuy\Console\Command\SetSellerIntegrationType">
        <arguments>
            <argument name="name" xsi:type="string">bigbuy:set:integration-type</argument>
        </arguments>
    </virtualType>
    <virtualType name="BigBuySetProductCategoryListCommand"
                 type="Comave\BigBuy\Console\Command\SetProductCategoryList">
        <arguments>
            <argument name="name" xsi:type="string">bigbuy:set-product:category-list</argument>
        </arguments>
    </virtualType>
    <virtualType name="BigBuyGetCategoryListCommand"
                 type="Comave\BigBuy\Console\Command\GetCategoryList">
        <arguments>
            <argument name="name" xsi:type="string">bigbuy:category:get-list</argument>
        </arguments>
    </virtualType>
    <virtualType name="BigBuyGetBrandListCommand" type="Comave\BigBuy\Console\Command\GetBrandList">
        <arguments>
            <argument name="name" xsi:type="string">bigbuy:brand:get-list</argument>
        </arguments>
    </virtualType>
    <type name="Magento\Framework\Console\CommandListInterface">
        <arguments>
            <argument name="commands" xsi:type="array">
                <item name="bigbuy:set:order-status" xsi:type="object">BigBuySetOrderStatusCommand</item>
                <item name="bigbuy:set:integration-type" xsi:type="object">BigBuySetSellerIntegrationTypeCommand</item>
                <item name="bigbuy:set-product:category-list" xsi:type="object">BigBuySetProductCategoryListCommand</item>
                <item name="bigbuy:brand:get-list" xsi:type="object">BigBuyGetBrandListCommand</item>
                <item name="bigbuy:category:get-list" xsi:type="object">BigBuyGetCategoryListCommand</item>
            </argument>
        </arguments>
    </type>
    <type name="Comave\BigBuy\Console\Command\SetProductCategoryList">
        <arguments>
            <argument name="categoryMatcherService" xsi:type="object">Comave\BigBuy\Service\CategoryMatcher</argument>
        </arguments>
    </type>
    <virtualType name="BigBuyCategoryMatcher" type="Comave\Logger\Model\ComaveLogger">
        <arguments>
            <argument name="name" xsi:type="string">BigBuyCategoryMatcher</argument>
            <argument name="loggerPath" xsi:type="string">category_matcher</argument>
        </arguments>
    </virtualType>
    <type name="Comave\BigBuy\Service\CategoryMatcher">
        <arguments>
            <argument xsi:type="object" name="logger">BigBuyCategoryMatcher</argument>
        </arguments>
    </type>
    <virtualType name="BigBuyCategoryRetriever" type="Comave\Logger\Model\ComaveLogger">
        <arguments>
            <argument name="name" xsi:type="string">BigBuyCategoryRetriever</argument>
            <argument name="loggerPath" xsi:type="string">category_retriever</argument>
        </arguments>
    </virtualType>
    <type name="Comave\BigBuy\Service\Category\Fetcher">
        <arguments>
            <argument xsi:type="object" name="logger">BigBuyCategoryRetriever</argument>
        </arguments>
    </type>
    <preference for="Comave\BigBuy\Api\Data\OrderLinkInterface" type="Comave\BigBuy\Model\OrderLink"/>
    <preference for="Comave\BigBuy\Api\OrderLinkRepositoryInterface" type="Comave\BigBuy\Model\OrderLinkRepository"/>
    <preference for="Comave\BigBuy\Api\OrderLinkListRepositoryInterface"
                type="Comave\BigBuy\Model\OrderLinkListRepository"/>
    <preference for="Comave\BigBuy\Api\Data\OrderLinkSearchResultInterface"
                type="Comave\BigBuy\Model\OrderLinkSearchResults"/>
    <type name="Magento\Framework\EntityManager\MetadataPool">
        <arguments>
            <argument name="metadata" xsi:type="array">
                <item name="Comave\BigBuy\Api\Data\OrderLinkInterface" xsi:type="array">
                    <item name="entityTableName" xsi:type="string">comave_bigbuy_order</item>
                    <item name="identifierField" xsi:type="string">link_id</item>
                </item>
            </argument>
        </arguments>
    </type>
    <type name="Comave\BigBuy\Model\ResourceModel\OrderLink">
        <arguments>
            <argument name="interfaceClass" xsi:type="string">Comave\BigBuy\Api\Data\OrderLinkInterface</argument>
        </arguments>
    </type>
    <type name="Comave\BigBuy\Model\ResourceModel\OrderLink\Collection">
        <arguments>
            <argument name="mainTable" xsi:type="string">comave_bigbuy_order</argument>
            <argument name="model" xsi:type="string">Comave\BigBuy\Model\OrderLink</argument>
            <argument name="resourceModel" xsi:type="string">Comave\BigBuy\Model\ResourceModel\OrderLink</argument>
            <argument name="idFieldName" xsi:type="string">link_id</argument>
            <argument name="eventPrefix" xsi:type="string">comave_bigbuy_order_collection</argument>
            <argument name="eventObject" xsi:type="string">comave_bigbuy_order_collection</argument>
            <argument name="interfaceClass" xsi:type="string">Comave\BigBuy\Api\Data\OrderLinkInterface</argument>
        </arguments>
    </type>
    <type name="Magento\Sales\Api\OrderRepositoryInterface">
        <plugin name="comave_bigbuy_plugin_model_order_repository"
                type="Comave\BigBuy\Plugin\Model\OrderRepositoryPlugin"/>
    </type>
</config>