<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\BigBuy\Service\Order;

use Comave\BigBuy\Model\ConfigProvider;
use Comave\BigBuyShipping\Service\BigBuyShippingService;
use Comave\SellerApi\Api\ConfigurableApiInterface;
use Comave\SellerApi\Api\ConfigurableApiInterfaceFactory;
use Comave\SellerApi\Api\OrderSynchroniseInterface;
use Comave\SellerApi\Service\RequestHandler;
use Comave\SellerApi\Service\SellerIdentity;
use Exception;
use Laminas\Http\Request;
use Magento\Framework\Serialize\SerializerInterface;
use Magento\Sales\Api\Data\OrderInterface;
use Magento\Sales\Api\Data\OrderItemInterface;
use Psr\Http\Client\ClientExceptionInterface;
use Psr\Log\LoggerInterface;

class SynchroniseService implements OrderSynchroniseInterface
{
    public const string BIG_BUY_IDENTIFIER = 'bigbuy';

    /**
     * @param \Comave\BigBuy\Service\Order\LinkService $orderLinkService
     * @param \Comave\BigBuy\Service\Order\NotificationService $notificationService
     * @param \Comave\SellerApi\Api\ConfigurableApiInterfaceFactory $configurableApiFactory
     * @param \Comave\SellerApi\Service\SellerIdentity $sellerIdentity
     * @param \Magento\Framework\Serialize\SerializerInterface $serializer
     * @param \Comave\BigBuy\Model\ConfigProvider $configProvider
     * @param \Comave\SellerApi\Service\RequestHandler $requestHandler
     * @param \Psr\Log\LoggerInterface $logger
     */
    public function __construct(
        private readonly LinkService $orderLinkService,
        private readonly NotificationService $notificationService,
        private readonly ConfigurableApiInterfaceFactory $configurableApiFactory,
        private readonly SellerIdentity $sellerIdentity,
        private readonly SerializerInterface $serializer,
        private readonly ConfigProvider $configProvider,
        private readonly RequestHandler $requestHandler,
        private readonly LoggerInterface $logger,
    ) {
    }

    /**
     * @param ConfigurableApiInterface $configurableApi
     * @param OrderInterface $order
     * @param OrderItemInterface[] $orderItems
     * @return void
     * @throws \Psr\Http\Client\ClientExceptionInterface
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function synchroniseOrder(
        ConfigurableApiInterface $configurableApi,
        OrderInterface $order,
        array $orderItems
    ): void {
        if (!$this->configProvider->isOrderSyncEnable()) {
            $this->logger->info(
                '[OrderSynchronizer] Not enabled, bypassing',
                [
                    'seller' => self::BIG_BUY_IDENTIFIER,
                ]
            );

            return;
        }

        $seller = $this->sellerIdentity->identify(self::BIG_BUY_IDENTIFIER);
        if ($seller === null) {
            $this->logger->warning(
                '[OrderSynchronizer] Unable to identify seller',
                [
                    'seller' => self::BIG_BUY_IDENTIFIER,
                ]
            );

            return;
        }

        if (!$this->isFullSellerOrder($order, $orderItems)) {
            $this->logger->warning(
                '[OrderSynchronizer] Synchronization of an order with multiple sellers is not supported.',
                [
                    'seller' => self::BIG_BUY_IDENTIFIER,
                    'order' => $order->getIncrementId(),
                ]
            );

            return;
        }

        $balance = $this->verifyBalance($order);
        if (!$balance['can_place']) {
            $subject = sprintf('Unable to place order #%s', $order->getIncrementId());
            $this->notificationService->send($subject, $balance['message'], $balance);
            $this->logger->warning(
                sprintf('[OrderSynchronizer] %s', $balance['message']),
                [
                    'seller' => self::BIG_BUY_IDENTIFIER,
                    'order' => $order->getIncrementId(),
                ]
            );

            return;
        }

        $orderSyncData = $this->extractSyncData($order, $orderItems);
        if (empty($orderSyncData)) {
            $this->logger->warning(
                '[OrderSynchronizer] Unable to identify seller products.',
                [
                    'seller' => self::BIG_BUY_IDENTIFIER,
                    'order' => $order->getIncrementId(),
                ]
            );

            return;
        }

        $configurableApi->setParams($this->serializer->serialize($orderSyncData));
        try {
            $results = $this->requestHandler->handleRequest($configurableApi);
            $content = $results->getResult()->getBody()->getContents();
            $decodedResult = [];
            if (!empty($content)) {
                $decodedResult = $this->serializer->unserialize($content);
            }

            if ($results->hasError()) {
                $message = 'There was an error processing the order';
                if (!empty($decodedResult['message'])) {
                    $decodedMessage = $this->serializer->unserialize($decodedResult['message']);
                    $message = $decodedMessage['info'] ?? $message;
                }
                throw new Exception($message);
            }
            $this->orderLinkService->createLink((int)$order->getId(), $decodedResult['order_id'] ?: '');
            $this->logger->info(
                '[OrderSynchronizer] Successfully synchronized order',
                [
                    'order' => $order->getIncrementId(),
                    'response' => $decodedResult,
                ]
            );
        } catch (Exception $e) {
            $this->notificationService->send(
                sprintf('Unable to place order #%s', $order->getIncrementId()),
                $e->getMessage(),
                $balance
            );
            $this->logger->error(
                '[OrderSynchronizer] Unable to synchronize order.',
                [
                    'error' => $e->getMessage(),
                    'order' => $order->getIncrementId(),
                ]
            );
        }
    }

    /**
     * @param \Magento\Sales\Api\Data\OrderInterface $order
     * @param array $orderItems
     * @return bool
     */
    private function isFullSellerOrder(OrderInterface $order, array $orderItems): bool
    {
        return count($order->getItems()) === count($orderItems);
    }

    /**
     * @param \Magento\Sales\Api\Data\OrderInterface $order
     * @param array $items
     * @return array[]
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    private function extractSyncData(OrderInterface $order, array $items): array
    {
        $orderData = [];
        $orderData['internalReference'] = $order->getIncrementId();
        $orderData['language'] = 'en';
        $orderData['paymentMethod'] = $order->getPayment()->getMethod();
        $orderData['carriers'] = [
            [
                'name' => BigBuyShippingService::getBigbuyMethodCodeFromShippingMethod($order->getShippingMethod())
            ],
        ];
        $orderData['shippingAddress'] = [
            'firstName' => $order->getShippingAddress()->getFirstname(),
            'lastName' => $order->getShippingAddress()->getLastname(),
            'country' => $order->getShippingAddress()->getCountryId(),
            'postcode' => $order->getShippingAddress()->getPostcode() ?? '',
            'town' => $order->getShippingAddress()->getCity() ?? '',
            'address' => implode(', ', $order->getShippingAddress()->getStreet() ?? ''),
            'phone' => $order->getShippingAddress()->getTelephone() ?? '',
            'email' => $order->getShippingAddress()->getEmail() ?? '',
            'vatNumber' => $order->getShippingAddress()->getVatId() ?? '',
            'companyName' => $order->getShippingAddress()->getCompany() ?? '',
            'comment' => $order->getShippingAddress()->getComment() ?? '',
        ];

        $products = [];
        foreach ($items as $item) {
            $product = [];
            $product['reference'] = $item->getSku();
            $product['quantity'] = (int)$item->getQtyOrdered();
            $products[] = $product;
        }
        $orderData['products'] = $products;

        return ['order' => $orderData];
    }

    /**
     * @param \Magento\Sales\Api\Data\OrderInterface $order
     * @return array
     */
    private function verifyBalance(OrderInterface $order): array
    {
        $balance = [
            'amount' => 0,
            'can_place' => false,
            'message' => 'Unable to verify balance',
        ];
        try {
            $configurableApi = $this->configurableApiFactory->create([
                'method' => Request::METHOD_GET,
                'endpoint' => $this->configProvider->getVerifyBalanceEndpoint(),
                'headers' => [
                    'Accept' => 'application/json',
                    'Content-Type' => 'application/json',
                    'Authorization' => sprintf('Bearer %s', $this->configProvider->getApiKey()),
                ],
            ]);
            $response = $this->requestHandler->handleRequest($configurableApi);
            if (!$response->hasError()) {
                $result = $this->serializer->unserialize(
                    $response->getResult()->getBody()->getContents()
                );
                $balance['amount'] = (float)$result;
                $balance['can_place'] = (float)$result >= (float)$order->getGrandTotal();
                $balance['message'] = $balance['can_place'] ?
                    'The balance is normal' :
                    'Unable to place order because the balance is too low';
            }

            return $balance;
        } catch (ClientExceptionInterface|Exception $exception) {
            $this->logger->warning("Unable to verify balance", [
                'exception' => $exception->getMessage(),
                'order_id' => $order->getIncrementId(),
            ]);
        }

        return $balance;
    }
}
