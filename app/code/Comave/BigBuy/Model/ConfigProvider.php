<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\BigBuy\Model;

use Comave\BigBuy\Model\Config\Source\Connection\Mode;
use Exception;
use Magento\Contact\Model\ConfigInterface;
use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Framework\Encryption\EncryptorInterface;
use Magento\Store\Model\ScopeInterface;

class ConfigProvider
{
    private const string XML_PATH_NOTIFICATION_ADMIN_EMAIL = 'notificationsettings/settings/adminemail';
    private const string XML_PATH_SELLER_BIGBUY_ENABLED = 'seller_settings/bigbuy_general/enable';
    private const string XML_PATH_SELLER_BIGBUY_ORDER_SYNC_ENABLED = 'seller_settings/bigbuy_api/order_sync';
    private const string XML_PATH_SELLER_BIGBUY_CATEGORY_SYNC_ENABLED = 'seller_settings/bigbuy_api/category_sync';
    private const string XML_PATH_SELLER_BIGBUY_BRAND_SYNC_ENABLED = 'seller_settings/bigbuy_api/brand_sync';
    private const string XML_PATH_SELLER_BIGBUY_TRACKING_SYNC_ENABLED = 'seller_settings/bigbuy_api/tracking_sync';
    private const string XML_PATH_SELLER_BIGBUY_GENERAL_API_CONNECTION_MODE = 'seller_settings/bigbuy_api/general_api_connection_mode';
    private const string XML_PATH_SELLER_BIGBUY_GENERAL_API_ENDPOINT = 'seller_settings/bigbuy_api/general_api_%s_endpoint';
    private const string XML_PATH_SELLER_BIGBUY_GENERAL_API_KEY = 'seller_settings/bigbuy_api/general_api_%s_key';
    private const string XML_PATH_SELLER_BIGBUY_EMAIL = 'seller_settings/bigbuy_general/seller_email';
    private const string XML_PATH_TRANS_EMAIL_IDENT_SUPPORT_NAME = 'trans_email/ident_support/name';
    private const string XML_PATH_TRANS_EMAIL_IDENT_SUPPORT_EMAIL = 'trans_email/ident_support/email';
    private const string XML_PATH_SELLER_BIGBUY_ORDER_STATUS_SYNC_ENABLED = 'seller_settings/bigbuy_api/order_status_sync';

    /**
     * @param \Magento\Framework\Encryption\EncryptorInterface $encryptor
     * @param \Magento\Framework\App\Config\ScopeConfigInterface $scopeConfig
     * @param \Comave\BigBuy\Model\Config\Source\Connection\Mode $modeTypes
     */
    public function __construct(
        private readonly EncryptorInterface $encryptor,
        private readonly ScopeConfigInterface $scopeConfig,
        private readonly Mode $modeTypes
    ) {
    }

    /**
     * Check if the extension is enabled
     */
    public function isEnabled(): bool
    {
        return $this->scopeConfig->isSetFlag(
            self::XML_PATH_SELLER_BIGBUY_ENABLED,
            ScopeInterface::SCOPE_STORE
        );
    }

    /**
     * Check if Order Sync is enabled or not
     *
     * @return bool
     */
    public function isOrderSyncEnable(): bool
    {
        return $this->isEnabled() && $this->scopeConfig->isSetFlag(
                self::XML_PATH_SELLER_BIGBUY_ORDER_SYNC_ENABLED,
                ScopeInterface::SCOPE_STORES
            );
    }

    /**
     * Check if Category Sync is enabled or not
     *
     * @return bool
     */
    public function isCategorySyncEnable(): bool
    {
        return $this->isEnabled() && $this->scopeConfig->isSetFlag(
                self::XML_PATH_SELLER_BIGBUY_CATEGORY_SYNC_ENABLED,
                ScopeInterface::SCOPE_STORES
            );
    }

    /**
     * Check if Brand Sync is enabled or not
     *
     * @return bool
     */
    public function isBrandSyncEnable(): bool
    {
        return $this->isEnabled() && $this->scopeConfig->isSetFlag(
                self::XML_PATH_SELLER_BIGBUY_BRAND_SYNC_ENABLED,
                ScopeInterface::SCOPE_STORES
            );
    }

    /**
     * Check if Order Tracking Sync is enabled or not
     *
     * @return bool
     */
    public function isTrackingSyncEnable(): bool
    {
        return $this->isEnabled() && $this->scopeConfig->isSetFlag(
                self::XML_PATH_SELLER_BIGBUY_TRACKING_SYNC_ENABLED,
                ScopeInterface::SCOPE_STORES
            );
    }

    /**
     * Get the BigBuy API endpoint
     *
     * @return string|null
     */
    public function getApiEndpoint(): ?string
    {
        return $this->scopeConfig->getValue(
            sprintf(self::XML_PATH_SELLER_BIGBUY_GENERAL_API_ENDPOINT, $this->getApiConnectionMode()),
            ScopeInterface::SCOPE_STORES
        );
    }

    /**
     * Get the BigBuy general api key
     *
     * @return string|null
     * @throws \Exception
     */
    public function getApiKey(): ?string
    {
        $apiKey = (string)$this->scopeConfig->getValue(
            sprintf(self::XML_PATH_SELLER_BIGBUY_GENERAL_API_KEY, $this->getApiConnectionMode()),
            ScopeInterface::SCOPE_STORES
        );
        if (!$apiKey) {
            throw new Exception("API Key is missing in configuration");
        }

        return $this->encryptor->decrypt($apiKey);
    }

    /**
     * @return string|null
     */
    public function getSellerEmail(): ?string
    {
        return $this->scopeConfig->getValue(
            self::XML_PATH_SELLER_BIGBUY_EMAIL,
            ScopeInterface::SCOPE_STORES
        );
    }

    /**
     * @return string
     */
    public function getApiConnectionMode(): string
    {
        $modeTypes = $this->modeTypes->toArray();
        $mode = (int)$this->scopeConfig->getValue(
            self::XML_PATH_SELLER_BIGBUY_GENERAL_API_CONNECTION_MODE,
            ScopeInterface::SCOPE_STORES
        );

        return $modeTypes[$mode];
    }

    /**
     * @return string
     */
    public function getOrderSyncEndpoint(): string
    {
        return sprintf(
            '%s/rest/order/create.json',
            $this->getApiEndpoint()
        );
    }

    /**
     * @param string $orderId
     * @return string
     */
    public function getOrderInfoEndpoint(string $orderId): string
    {
        return sprintf(
            '%s/rest/order/%s.json?language=en',
            $this->getApiEndpoint(),
            $orderId
        );
    }

    /**
     * @param string $orderId
     * @return string
     */
    public function getTrackingSyncEndpoint(string $orderId): string
    {
        return sprintf(
            '%s/rest/tracking/order/%s.json',
            $this->getApiEndpoint(),
            $orderId
        );
    }

    /**
     * @return string
     */
    public function getCategorySyncEndpoint(): string
    {
        return sprintf(
            '%s/rest/catalog/taxonomies.json?isoCode=en',
            $this->getApiEndpoint()
        );
    }

    /**
     * @return string
     */
    public function getBrandSyncEndpoint(): string
    {
        return sprintf(
            '%s/rest/catalog/manufacturers.json?isoCode=en',
            $this->getApiEndpoint()
        );
    }

    /**
     * @return string
     */
    public function getVerifyBalanceEndpoint(): string
    {
        return sprintf(
            '%s/rest/user/purse.json',
            $this->getApiEndpoint()
        );
    }

    /**
     * @return string
     */
    public function getNotificationSenderName(): string
    {
        return (string)$this->scopeConfig->getValue(
            self::XML_PATH_TRANS_EMAIL_IDENT_SUPPORT_NAME,
            ScopeInterface::SCOPE_STORES
        );
    }

    /**
     * @return string
     */
    public function getNotificationSenderEmail(): string
    {
        return (string)$this->scopeConfig->getValue(
            self::XML_PATH_TRANS_EMAIL_IDENT_SUPPORT_EMAIL,
            ScopeInterface::SCOPE_STORES
        );
    }

    /**
     * @return string
     */
    public function getNotificationSupportEmail(): string
    {
        return (string)$this->scopeConfig->getValue(
            ConfigInterface::XML_PATH_EMAIL_RECIPIENT,
            ScopeInterface::SCOPE_STORES
        );
    }

    /**
     * @return string
     */
    public function getNotificationAdminEmail(): string
    {
        return (string)$this->scopeConfig->getValue(
            self::XML_PATH_NOTIFICATION_ADMIN_EMAIL,
            ScopeInterface::SCOPE_STORES
        );
    }

    /**
     * @return bool
     */
    public function isOrderStatusSyncEnable(): bool
    {
        return $this->isEnabled() && $this->scopeConfig->isSetFlag(
                self::XML_PATH_SELLER_BIGBUY_ORDER_STATUS_SYNC_ENABLED,
                ScopeInterface::SCOPE_STORES
            );
    }

    /**
     * @return string
     */
    public function getOrderStatusSyncEndpoint(): string
    {
        return sprintf(
            '%s/rest/order/orderstatuses.json?language=en',
            $this->getApiEndpoint()
        );
    }
}
