<?php

declare(strict_types=1);

namespace Comave\RmaGraphQl\Plugin;

use Comave\Rma\Service\EmailSender;
use Comave\Rma\Service\SellerReturnAddressProvider;
use Comave\SellerApi\Observer\SellerOrderSync;
use Comave\SellerApi\Service\OrderSellersIdentifier;
use Magento\Framework\GraphQl\Config\Element\Field;
use Magento\Framework\GraphQl\Exception\GraphQlInputException;
use Magento\Framework\GraphQl\Query\Uid;
use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;
use Magento\RmaGraphQl\Model\Resolver\RequestReturn;
use Magento\Sales\Api\OrderRepositoryInterface;
use Psr\Log\LoggerInterface;

class CheckReturnAddress
{
    /**
     * @param OrderRepositoryInterface $orderRepository
     * @param SellerReturnAddressProvider $sellerReturnAddressProvider
     * @param OrderSellersIdentifier $orderSellersIdentifier
     * @param Uid $idEncoder
     */
    public function __construct(
        private readonly OrderRepositoryInterface $orderRepository,
        private readonly SellerReturnAddressProvider $sellerReturnAddressProvider,
        private readonly OrderSellersIdentifier $orderSellersIdentifier,
        private readonly Uid $idEncoder
    ) {
    }

    /**
     * @param RequestReturn $requestReturnResolver
     * @param Field $field
     * @param $context
     * @param ResolveInfo $info
     * @param array|null $value
     * @param array|null $args
     * @return void
     * @throws GraphQlInputException
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function beforeResolve(
        RequestReturn $requestReturnResolver,
        Field $field,
        $context,
        ResolveInfo $info,
        array $value = null,
        array $args = null
    ): void {
        if (!isset($args['input']['order_uid'])) {
            return;
        }

        $orderId = $this->idEncoder->decode($args['input']['order_uid']);
        $order = $this->orderRepository->get((int) $orderId);
        $sellerItems = $this->orderSellersIdentifier->get($order);

        if (empty($sellerItems)) {
            return;
        }

        //@todo - split order
        $sellerReturnAddresses = $this->sellerReturnAddressProvider->get($order);

        if (empty($sellerReturnAddresses)) {
            throw new GraphQlInputException(__('No return address found for current order'));
        }
    }
}
