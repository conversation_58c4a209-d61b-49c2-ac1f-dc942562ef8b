<?php

declare(strict_types=1);

namespace Comave\RmaGraphQl\Plugin;

use Comave\Rma\Service\EmailSender;
use Magento\Framework\GraphQl\Config\Element\Field;
use Magento\RmaGraphQl\Model\Resolver\RequestReturn;
use Psr\Log\LoggerInterface;

class SendNotificationEmails
{
    /**
     * @param EmailSender $emailSender
     * @param LoggerInterface $logger
     */
    public function __construct(
        private readonly EmailSender $emailSender,
        private readonly LoggerInterface $logger,
    ) {
    }

    /**
     * @param RequestReturn $requestReturnResolver
     * @param array $rmaResult
     * @param Field $field
     * @param mixed $context
     * @return array
     */
    public function afterResolve(
        RequestReturn $requestReturnResolver,
        array $rmaResult,
        Field $field,
        \Magento\GraphQl\Model\Query\ContextInterface $context,
    ): array {
        $websiteId = $context->getExtensionAttributes()?->getStore()->getWebsiteId() ?? null;

        try {
            $this->emailSender->sendSellerEmail($rmaResult['model'], $websiteId);
        } catch (\Exception $e) {
            $this->logger->warning(
                'Unable to send new Seller RMA Email',
                [
                    'message' => $e->getMessage(),
                ]
            );
        }

        return $rmaResult;
    }
}
