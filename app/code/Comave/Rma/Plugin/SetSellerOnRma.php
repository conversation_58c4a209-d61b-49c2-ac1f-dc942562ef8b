<?php

declare(strict_types=1);

namespace Comave\Rma\Plugin;

use Comave\Rma\Service\SellerReturnAddressProvider;
use Magento\Customer\Api\CustomerRepositoryInterface;
use Magento\Rma\Api\Data\RmaExtensionInterface;
use Magento\Rma\Api\Data\RmaExtensionInterfaceFactory;
use Magento\Rma\Api\Data\RmaInterface;
use Magento\Sales\Api\OrderRepositoryInterface;

class SetSellerOnRma
{
    /**
     * @param RmaExtensionInterfaceFactory $extensionFactory
     * @param SellerReturnAddressProvider $sellerReturnAddressProvider
     * @param OrderRepositoryInterface $orderRepository
     * @param CustomerRepositoryInterface $customerRepository
     */
    public function __construct(
        private readonly RmaExtensionInterfaceFactory $extensionFactory,
        private readonly SellerReturnAddressProvider $sellerReturnAddressProvider,
        private readonly OrderRepositoryInterface $orderRepository,
        private readonly CustomerRepositoryInterface $customerRepository
    ) {
    }

    /**
     * @param RmaInterface $rma
     * @param RmaExtensionInterface|null $extension
     * @return RmaExtensionInterface
     */
    public function afterGetExtensionAttributes(
        RmaInterface $rma,
        ?RmaExtensionInterface $extension = null
    ): RmaExtensionInterface {
        if ($extension === null) {
            $extension = $this->extensionFactory->create();
        }

        if (empty($rma->getOrderId()) || !$rma->getEntityId()) {
            return $extension;
        }

        $rmaSeller = $extension->getSeller();

        if (!empty($rmaSeller)) {
            $rma->setExtensionAttributes($extension);

            return $extension;
        }

        try {
            $order = $this->orderRepository->get($rma->getOrderId());
            $sellerReturnAddresses = $this->sellerReturnAddressProvider->get($order);

            if (empty($sellerReturnAddresses)) {
                return $extension;
            }

            //@todo - split order check
            $currentAddress = current($sellerReturnAddresses);
            $seller = $this->customerRepository->getById($currentAddress['parent_id']);
            $extension->setSeller($seller);
            $rma->setExtensionAttributes($extension);

            return $extension;
        } catch (\Exception) {
            return $extension;
        }
    }
}
