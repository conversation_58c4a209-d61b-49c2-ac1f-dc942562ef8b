<?php
/**
 * Copyright © Commercial Avenue
 */
namespace Comave\Club\Controller\Adminhtml\Club;

use Comave\Club\Api\ClubRepositoryInterface;
use Magento\Backend\App\Action;
use Magento\Backend\App\Action\Context;
use Magento\Backend\Helper\Js;
use Magento\Catalog\Model\Product\Url;
use Magento\Framework\App\Filesystem\DirectoryList;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\File\UploaderFactory;
use Magento\Framework\Filesystem;
use Psr\Log\LoggerInterface;
use Comave\Club\Model\ClubFactory;

class Save extends Action
{
    public function __construct(
        Context $context,
        private readonly Filesystem $fileSystem,
        private readonly Js $jsHelper,
        private readonly LoggerInterface $logger,
        private readonly Url $productUrl,
        private readonly ClubRepositoryInterface $clubRepository,
        private readonly UploaderFactory $uploaderFactory,
        private readonly ClubFactory $clubFactory
    ) {
        parent::__construct($context);
    }

    /**
     * {@inheritdoc}
     */
    protected function _isAllowed()
    {
        return $this->_authorization->isAllowed('Comave_Club::club_save');
    }

    /**
     * Save action
     *
     * @return \Magento\Framework\Controller\ResultInterface
     */
    public function execute()
    {
        $data = $this->getRequest()->getPostValue();
        /** @var \Magento\Backend\Model\View\Result\Redirect $resultRedirect */
        $resultRedirect = $this->resultRedirectFactory->create();
        $isNew = false;

        if ($data) {
            $id = $this->getRequest()->getParam('club_id');
            $club_image = $club_clogo = $club_banner = $club_watermark_image ="";
            if ($id) {
                $model = $this->clubRepository->get($id);
                $club_image = $model->getImage();
                $club_clogo = $model->getClogo();
                $club_banner = $model->getClubBanner();
                $club_watermark_image = $model->getClubWatermarkImage();
            }else{
                $model = $this->clubFactory->create();
                $isNew = true;
            }

            $prefix = $this->getRequest()->getParam('club_prefix');
             if($data['club_prefix']=='') {
                $data['club_prefix'] = $prefix;
             }

            $ccount = $this->getRequest()->getParam('club_count');
            if($data['club_count']=='') {
                $data['club_count'] = $ccount;
            }

            /** @var \Magento\Framework\Filesystem\Directory\Read $mediaDirectory */
            $mediaDirectory = $this->_objectManager->get('Magento\Framework\Filesystem')
            ->getDirectoryRead(DirectoryList::MEDIA);
            $mediaFolder = 'comave/club/';
            $path = $mediaDirectory->getAbsolutePath($mediaFolder);

            // Delete, Upload Image
            $imagePath = '';
            if(!$isNew){
                $imagePath = $mediaDirectory->getAbsolutePath($model->getImage());
            }

            if(isset($data['image']['delete']) && file_exists($imagePath)){
                unlink($imagePath);
                $data['image'] = '';
                if($club_image && $club_clogo && $club_image == $club_clogo){
                    $data['clogo'] = '';
                }
            }
            if(isset($data['image']) && is_array($data['image'])){
                unset($data['image']);
            }

            if($image = $this->uploadImage('image')){
                $data['image'] = $image;
            }

            // Delete, Upload club logo
            $clogoPath = '';
            if(!$isNew){
                $clogoPath = $mediaDirectory->getAbsolutePath($model->getClogo());
            }
            if(isset($data['clogo']['delete']) && file_exists($clogoPath)){
                unlink($clogoPath);
                $data['clogo'] = '';
                if($club_image && $club_clogo && $club_image == $club_clogo){
                    $data['image'] = '';
                }
            }
            if(isset($data['clogo']) && is_array($data['clogo'])){
                unset($data['clogo']);
            }
            if($clogo = $this->uploadImage('clogo')){
                $data['clogo'] = $clogo;
            }

            // Delete, Upload club banner
            $club_bannerPath = '';
            if(!$isNew){
                $club_bannerPath = $mediaDirectory->getAbsolutePath($model->getClubBanner());
            }
            if(isset($data['club_banner']['delete']) && file_exists($club_bannerPath)){
                unlink($club_bannerPath);
                $data['club_banner'] = '';
                if($club_banner && $club_watermark_image && $club_banner == $club_watermark_image){
                    $data['club_watermark_image'] = '';
                }
            }

            if(isset($data['club_banner']) && is_array($data['club_banner'])){
                unset($data['club_banner']);
            }

            if($club_banner = $this->uploadImage('club_banner')){
                $data['club_banner'] = $club_banner;
            }

            // Delete, Upload club watermark image
            $club_watermark_imagePath = '';
            if(!$isNew){
                $club_watermark_imagePath = $mediaDirectory->getAbsolutePath($model->getClubWatermarkImage());

            }
            if(isset($data['club_watermark_image']['delete']) && file_exists($club_watermark_imagePath)){
                unlink($club_watermark_imagePath);
                $data['club_watermark_image'] = '';
                if($club_banner && $club_watermark_image && $club_banner == $club_watermark_image){
                    $data['club_banner'] = '';
                }
            }

            if(isset($data['club_watermark_image']) && is_array($data['club_watermark_image'])){
                unset($data['club_watermark_image']);
            }

            if($club_watermark_image = $this->uploadImage('club_watermark_image')){
                $data['club_watermark_image'] = $club_watermark_image;
            }

            if($data['url_key']=='') {
                $data['url_key'] = $data['name'];
            }
            $url_key = $this->productUrl->formatUrlKey($data['url_key']);
            $data['url_key'] = $url_key;

            $links = $this->getRequest()->getPost('links');
            $links = is_array($links) ? $links : [];
            if(!empty($links) && isset($links['related'])){
                $products = $this->jsHelper->decodeGridSerializedInput($links['related']);
                $data['products'] = $products;
            }

            try {
                $model->setData($data);
                $this->clubRepository->save($model);
                $this->messageManager->addSuccessMessage(__('You saved this club.'));
                $this->_getSession()->setFormData(false);
                if ($this->getRequest()->getParam('back')) {
                    return $resultRedirect->setPath(
                        '*/*/edit',
                        ['club_id' => $model->getId(), '_current' => true]
                    );
                }

                return $resultRedirect->setPath('*/*/');
            } catch (LocalizedException $e) {
                $this->messageManager->addErrorMessage($e->getMessage());
            } catch (\RuntimeException $e) {
                $this->messageManager->addErrorMessage($e->getMessage());
            } catch (\Exception $e) {
                $this->messageManager->addErrorMessage(
                    $e,
                    __('Something went wrong while saving the club.')
                );
            }

            $this->_getSession()->setFormData($data);
            return $resultRedirect->setPath(
                '*/*/edit',
                [
                    'club_id' => $this->getRequest()->getParam('club_id')
                ]
            );
        }

        return $resultRedirect->setPath('*/*/');
    }

    public function uploadImage($fieldId = 'image')
    {
        /** @var \Magento\Backend\Model\View\Result\Redirect $resultRedirect */
        $resultRedirect = $this->resultRedirectFactory->create();

        $files = $this->_request->getFiles($fieldId);
        if (!empty($files) && $files['name'] != '') {
            $uploader = $this->uploaderFactory->create(['fileId' => $fieldId]);

            /** @var \Magento\Framework\Filesystem\Directory\Read $mediaDirectory */
            $mediaDirectory = $this->fileSystem->getDirectoryRead(DirectoryList::MEDIA);
            $mediaFolder = 'comave/club/';
            try {
                $uploader->setAllowedExtensions(array('jpeg', 'jpg', 'png', 'gif', 'JPEG', 'JPG', 'PNG', 'GIF','webp', 'WEBP', 'svg', 'SVG', 'avif', 'AVIF', 'jfif', 'JFIF'));
                $uploader->setAllowRenameFiles(true);
                $uploader->setFilesDispersion(false);
                $result = $uploader->save($mediaDirectory->getAbsolutePath($mediaFolder));

                return $mediaFolder . $result['name'];
            } catch (\Exception $e) {
                $this->logger->critical($e);
                $this->messageManager->addErrorMessage(
                    $e->getMessage()
                );

                return $resultRedirect->setPath(
                    '*/*/edit',
                    [
                        'club_id' => $this->getRequest()->getParam('club_id')
                    ]
                );
            }
        }

        return '';
    }
}
