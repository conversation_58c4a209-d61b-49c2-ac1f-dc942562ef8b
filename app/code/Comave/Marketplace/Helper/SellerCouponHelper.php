<?php
/**
 * Comave Marketplace Seller Coupon Helper
 * Simplified version moved from Webkul\MpSellerCoupons\Helper\Data
 */
namespace Comave\Marketplace\Helper;

use Magento\Store\Model\ScopeInterface;
use Webkul\Marketplace\Model\ProductFactory as MpProductModel;

/**
 * Seller Coupon data helper
 */
class SellerCouponHelper extends \Magento\Framework\App\Helper\AbstractHelper
{
    public const DELIMITED = ",";

    /**
     * @var \Webkul\Marketplace\Helper\Data
     */
    protected $mpDataHelper;

    /**
     * @var MpProductModel
     */
    protected $mpProductModel;

    /**
     * @var \Magento\SalesRule\Model\RuleFactory
     */
    protected $ruleFactory;

    /**
     * @var \Magento\SalesRule\Model\CouponFactory
     */
    protected $couponFactory;

    /**
     * Init function
     *
     * @param \Magento\Framework\App\Helper\Context $context
     * @param \Webkul\Marketplace\Helper\Data $mpDataHelper
     * @param \Magento\SalesRule\Model\RuleFactory $ruleFactory
     * @param \Magento\SalesRule\Model\CouponFactory $couponFactory
     * @param MpProductModel $mpProductModel
     */
    public function __construct(
        \Magento\Framework\App\Helper\Context $context,
        \Webkul\Marketplace\Helper\Data $mpDataHelper,
        \Magento\SalesRule\Model\RuleFactory $ruleFactory,
        \Magento\SalesRule\Model\CouponFactory $couponFactory,
        MpProductModel $mpProductModel
    ) {
        $this->mpDataHelper = $mpDataHelper;
        $this->ruleFactory = $ruleFactory;
        $this->couponFactory = $couponFactory;
        $this->mpProductModel = $mpProductModel;
        parent::__construct($context);
    }

    /**
     * ModuleConfigStatus is used to check the module's configuration status
     * For now, we'll return false to disable seller coupon functionality
     * This can be enabled later if needed
     *
     * @return bool
     */
    public function moduleConfigStatus()
    {
        // Disable seller coupon functionality for now
        // Can be re-enabled by adding configuration or returning true
        return false;
    }

    /**
     * GetSellerData
     *
     * @param \Magento\Quote\Model\Quote $quote
     *
     * @return array
     */
    public function getSellerData($quote)
    {
        $sellerData = [];
        foreach ($quote->getAllItems() as $item) {
            $sellerId = 0;
            $productId = $item->getProductId();
            $querydata = $this->mpProductModel->create()
                        ->getCollection()
                        ->addFieldToFilter(
                            'mageproduct_id',
                            ['eq' => $productId]
                        )
                        ->addFieldToFilter(
                            'status',
                            ['eq' => 1]
                        )
                        ->addFieldToSelect('seller_id')
                        ->setOrder('mageproduct_id');
            
            foreach($querydata as $prodata){
                $sellerId = $prodata->getSellerId();
            }
            
            if ($sellerId) {
                if (isset($sellerData[$sellerId]['item_id'])) {
                    $sellerData[$sellerId]['item_id'] = $sellerData[$sellerId]['item_id'].','.$item->getId();
                } else {
                    $sellerData[$sellerId]['item_id'] = $item->getId();
                }
            }
        }
        return $sellerData;
    }

    /**
     * GetCouponSellerWise is used to get the seller wise applied Coupons
     * For now, returns empty array since seller coupon functionality is disabled
     *
     * @param  string $couponCode [applied coupon code]
     * @return array
     */
    public function getCouponSellerWise($couponCode)
    {
        // Return empty array since seller coupon functionality is disabled
        // This method can be implemented later if seller coupons are re-enabled
        return [];
    }

    /**
     * Check if coupon is valid
     * Placeholder method for future implementation
     *
     * @param object $ruleData
     * @param string $couponCode
     * @return bool
     */
    protected function checkIfCouponValid($ruleData, $couponCode)
    {
        // Placeholder implementation
        // Can be expanded later if needed
        return false;
    }
}
