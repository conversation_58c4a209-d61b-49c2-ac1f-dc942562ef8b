<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <preference for="Comave\CategoryCommission\Api\Data\CommissionInterface" type="Comave\CategoryCommission\Model\Commission" />
    <preference for="Comave\CategoryCommission\Api\CommissionRepositoryInterface" type="Comave\CategoryCommission\Model\CommissionRepository" />
    <preference for="Comave\CategoryCommission\Api\Data\CommissionSearchResultsInterface" type="Magento\Framework\Api\SearchResults" />
    <type name="Magento\Catalog\Model\CategoryRepository">
        <plugin name="category_commission_get" type="Comave\CategoryCommission\Plugin\Catalog\Model\CategoryRepository\Get"/>
        <plugin name="category_commission_save" type="Comave\CategoryCommission\Plugin\Catalog\Model\CategoryRepository\Save"/>
        <plugin name="category_commission_getlist" type="Comave\CategoryCommission\Plugin\Catalog\Model\CategoryRepository\GetList"/>
    </type>
    <type name="Magento\Catalog\Model\Category">
        <plugin name="category_commission_load" type="Comave\CategoryCommission\Plugin\Catalog\Model\Category\PluginLoadCategory"/>
    </type>
</config>
