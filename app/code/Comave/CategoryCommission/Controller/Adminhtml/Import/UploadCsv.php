<?php
declare(strict_types=1);

namespace Comave\CategoryCommission\Controller\Adminhtml\Import;

use Comave\CategoryCommission\Model\CommissionFactory;
use Magento\Backend\App\Action\Context;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\View\Result\PageFactory;
use Magento\Framework\File\Csv;
use Magento\MediaStorage\Model\File\UploaderFactory;
use Comave\CategoryCommission\Api\CommissionRepositoryInterface;
use Psr\Log\LoggerInterface;

class UploadCsv extends \Magento\Backend\App\Action
{

    /**
     * @param Csv $csvProcessor
     * @param UploaderFactory $fileUploaderFactory
     * @param Context $context
     * @param PageFactory $resultPageFactory
     * @param CommissionRepositoryInterface $commissionRepository
     * @param LoggerInterface $logger
     */
    public function __construct(
        private Csv             $csvProcessor,
        private UploaderFactory $fileUploaderFactory,
        private Context         $context,
        private PageFactory     $resultPageFactory,
        private CommissionRepositoryInterface $commissionRepository,
        private readonly CommissionFactory $commissionFactory,
        protected LoggerInterface $logger

    ) {
        parent::__construct($context);
    }

    public function execute()
    {
        $data = $this->getRequest()->getFiles('commission_file');
        if (!isset($data['name'])) {
            $this->messageManager->addErrorMessage(__('No file name given'));
            return $this->resultRedirectFactory->create()->setPath('commission/index/index');
        }
        try {
            $tempFile = $data['tmp_name'];
            $importData = $this->csvProcessor->getData($tempFile);
            $lines = 0;
            $errorCount = 0;
            foreach ($importData as $rowIndex => $dataRow) {
                if ($rowIndex == 0) {
                    if (!$this->getValidateColumns($dataRow)) {
                        return $this->resultRedirectFactory->create()->setPath('commission/index/index');
                    }
                    continue;
                }
                try {
                    if ($dataRow[0]) {
                        $commission = $this->commissionRepository->getById($dataRow[0]);
                    } else {
                        $commission = $this->commissionFactory->create();
                    }
                    $commission->setCategoryId($dataRow[1]);
                    $commission->setCategoryName($dataRow[2]);
                    $commission->setStoreId($dataRow[3]);
                    $commission->setType($dataRow[4]);
                    $commission->setValue($dataRow[5]);
                    $this->commissionRepository->save($commission);
                } catch (LocalizedException $e) {
                    $errorCount++;
                    $this->messageManager->addErrorMessage("Error on line " . $lines);
                    if ($errorCount > 10) {
                        return $this->resultRedirectFactory->create()->setPath('commission/index/index');
                    }
                }
                $lines++;
            }
            $this->messageManager->addSuccessMessage(__("Successfull import of " . $lines));
        }
        catch
            (\Exception $e) {
                $this->messageManager->addErrorMessage($e->getMessage());
                return $this->resultRedirectFactory->create()->setPath('commission/index/index');
            }
        return $this->resultRedirectFactory->create()->setPath('commission/index/index');
    }

    /**
     * @param array $header
     * @return bool
     */
    private function getValidateColumns(array $header): bool
    {
        if(count($header) !== 6){
            $this->messageManager->addErrorMessage(__('The file does not contain all the required fields.
             Please download the current, make the required changes, and upload the same file'));
            return false;
        }
        return true;
    }
}
