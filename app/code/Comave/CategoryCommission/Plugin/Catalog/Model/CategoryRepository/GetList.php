<?php
declare(strict_types=1);

namespace Comave\CategoryCommission\Plugin\Catalog\Model\CategoryRepository;

use Comave\CategoryCommission\Api\CommissionRepositoryInterface;

/**
 * Class GetList
 */
class GetList
{
    /**
     * Get constructor.
     * @param CommissionRepositoryInterface $commissionRepository
     */
    public function __construct(
        private    CommissionRepositoryInterface $commissionRepository
    ) {
    }

    /**
     * @param \Magento\Catalog\Api\CategoryRepositoryInterface $subject
     * @param \Magento\Catalog\Api\Data\CategorySearchResultsInterface $searchCriteria
     * @return \Magento\Catalog\Api\Data\CategorySearchResultsInterface
     */
    public function afterGetList(
        \Magento\Catalog\Api\CategoryRepositoryInterface $subject,
        \Magento\Catalog\Api\Data\CategorySearchResultsInterface $searchCriteria
    ) {
        $categories = [];
        foreach ($searchCriteria->getItems() as $entity) {
            $commission = $this->commissionRepository->getByCategoryId((int)$entity->getId());
            if (isset($commission[0])){
                $entity->setData('commission_type', $commission[0]->getType());
                $entity->setData('commission_value', $commission[0]->getValue());
            }
            $extensionAttributes = $entity->getExtensionAttributes();
            $extensionAttributes->setCommission($commission);
            $entity->setExtensionAttributes($extensionAttributes);
            $categories[] = $entity;
        }

        $searchCriteria->setItems($categories);
        return $searchCriteria;
    }
}
