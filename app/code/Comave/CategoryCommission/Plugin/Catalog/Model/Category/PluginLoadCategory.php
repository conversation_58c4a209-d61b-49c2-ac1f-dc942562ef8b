<?php
declare(strict_types=1);
namespace Comave\CategoryCommission\Plugin\Catalog\Model\Category;

use Comave\CategoryCommission\Api\CommissionRepositoryInterface;

/**
 * Class Load
 */
class PluginLoadCategory
{

    /**
     * Get constructor.
     * @param CommissionRepositoryInterface $commissionRepository
     */
    public function __construct(
        private    CommissionRepositoryInterface $commissionRepository
    ) {
    }

    /**
     * @param \Magento\Catalog\Api\Data\CategoryInterface $subject
     * @param \Magento\Catalog\Api\Data\CategoryInterface $entity
     * @return \Magento\Catalog\Api\Data\CategoryInterface
     */
    public function afterLoad(\Magento\Catalog\Api\Data\CategoryInterface $subject, \Magento\Catalog\Api\Data\CategoryInterface $entity)
    {

        $commission = $this->commissionRepository->getByCategoryId((int)$entity->getEntityId());
        if (isset($commission[0])){
            $entity->setData('commission_type', $commission[0]->getType());
            $entity->setData('commission_value', $commission[0]->getValue());
        }
        $extensionAttributes = $entity->getExtensionAttributes();
        /** get current extension attributes from entity **/
        $extensionAttributes->setCommission($commission);
        $entity->setExtensionAttributes($extensionAttributes);

        return $entity;
    }
}
