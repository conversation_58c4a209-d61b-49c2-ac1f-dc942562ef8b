<?php
declare(strict_types=1);

namespace Comave\CategoryCommission\Api\Data;

interface CommissionInterface extends \Magento\Framework\Api\ExtensibleDataInterface
{
	const ENTITY_ID = 'entity_id';
    const CATEGORY_ID = 'category_id';
    const CATEGORY_NAME = 'category_name';
    const TYPE = 'type';
    const VALUE = 'value';
    const STORE_ID = 'store_id';

    /**
     * Set entity_id
     * @param int $entityId
     * @return \Comave\CategoryCommission\Api\Data\CommissionInterface
     */
    public function setEntityId($entityId);

    /**
     * @return int|null
     */
    public function getEntityId();

    /**
     * Set categoryId
     * @param int $categoryId
     * @return \Comave\CategoryCommission\Api\Data\CommissionInterface
     */
    public function setCategoryId($categoryId);

    /**
     * @return int|null
     */
    public function getCategoryId();

    /**
     * Set categoryName
     * @param string $categoryName
     * @return \Comave\CategoryCommission\Api\Data\CommissionInterface
     */
    public function setCategoryName($categoryName);

    /**
     * @return string|null
     */
    public function getCategoryName();

    /**
     * Set type
     * @param int $type
     * @return \Comave\CategoryCommission\Api\Data\CommissionInterface
     */
    public function setType($type);

    /**
     * @return int|null
     */
    public function getType();

    /**
     * Set type
     * @param string $value
     * @return \Comave\CategoryCommission\Api\Data\CommissionInterface
     */
    public function setValue($value);

    /**
     * @return string|null
     */
    public function getValue();

    /**
     * Set type
     * @param int $storeId
     * @return \Comave\CategoryCommission\Api\Data\CommissionInterface
     */
    public function setStoreId($storeId);

    /**
     * @return int|null
     */
    public function getStoreId();

    /**
     * Retrieve existing extension attributes object or create a new one.
     * @return \Comave\CategoryCommission\Api\Data\CommissionExtensionInterface|null
     */
    public function getExtensionAttributes();

    /**
     * Set an extension attributes object.
     * @param \Comave\CategoryCommission\Api\Data\CommissionExtensionInterface $extensionAttributes
     * @return $this
     */
    public function setExtensionAttributes(
        \Comave\CategoryCommission\Api\Data\CommissionExtensionInterface $extensionAttributes
    );
}

