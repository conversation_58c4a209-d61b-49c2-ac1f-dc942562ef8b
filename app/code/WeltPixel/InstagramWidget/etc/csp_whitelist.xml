<?xml version="1.0" encoding="UTF-8"?>
<csp_whitelist xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
               xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Csp:etc/csp_whitelist.xsd">
    <policies>
        <policy id="script-src">
            <values>
                <value id="weltpixel_instagramwidgest_apiscript" type="host">*.instagram.com</value>
            </values>
        </policy>
        <policy id="img-src">
            <values>
                <value id="weltpixel_instagramwidgest_images" type="host">*.cdninstagram.com</value>
                <value id="weltpixel_instagramwidgest_imagesfb" type="host">*.fbcdn.net</value>
            </values>
        </policy>
        <policy id="connect-src">
            <values>
                <value id="weltpixel_instagramwidgest_apiscript" type="host">*.instagram.com</value>
                <value id="weltpixel_instagramwidgest_googleproxy" type="host">*.googleusercontent.com</value>
            </values>
        </policy>
        <policy id="media-src">
            <values>
                <value id="weltpixel_instagramwidgest_videos" type="host">*.cdninstagram.com</value>
            </values>
        </policy>
    </policies>
</csp_whitelist>
