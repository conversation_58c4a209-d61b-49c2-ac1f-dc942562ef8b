<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
    <system>
        <section id="weltpixel_instagram" translate="label" type="text" sortOrder="170" showInDefault="1" showInWebsite="1" showInStore="1">
            <label>Instagram Widget</label>
            <tab>weltpixel</tab>
            <resource>WeltPixel_Instagram::InstagramSettings</resource>
            <group id="general" translate="label" type="text" sortOrder="10" showInDefault="1" showInWebsite="0" showInStore="0">
                <label>Instagram Widget Settings</label>
                <field id="tokens" translate="label" type="select" sortOrder="0" showInDefault="1" showInWebsite="0" showInStore="0">
                    <label>Tokens</label>
                    <frontend_model>WeltPixel\InstagramWidget\Block\Adminhtml\Form\Field\InstagramToken</frontend_model>
                    <backend_model>Magento\Config\Model\Config\Backend\Serialized\ArraySerialized</backend_model>
                </field>
            </group>
        </section>
    </system>
</config>
