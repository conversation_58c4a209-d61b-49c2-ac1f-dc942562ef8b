<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:Acl/etc/acl.xsd">
    <acl>
        <resources>
            <resource id="Magento_Backend::admin">
                <resource id="Magento_Backend::stores">
                    <resource id="Magento_Backend::stores_settings">
                        <resource id="Magento_Config::config">
                            <resource id="WeltPixel_Instagram::InstagramSettings" title="WeltPixel Instagram" sortOrder="270" />
                        </resource>
                    </resource>
                </resource>
                <resource id="WeltPixel_Backend::WeltPixel" >
                    <resource id="WeltPixel_Instagram::Instagram_Label" sortOrder="80" title="Instagram Widget" >
                        <resource id="WeltPixel_Instagram::Instagram" sortOrder="10" title="Instagram Widget Settings" />
                    </resource>
                </resource>
            </resource>
        </resources>
    </acl>
</config>
