"Token Name","Token Name"
"Token Value","Token Value"
"Add Token","Add Token"
"Basic API","Basic API"
"Javascript Fetching (Deprecated)","Javascript Fetching (Deprecated)"
"Old Api (Deprecated)","Old Api (Deprecated)"
User,User
Tag,Tag
"Original Size","Original Size"
"As they come from Instagram","As they come from Instagram"
"Newest to oldest","Newest to oldest"
"Oldest to newest","Oldest to newest"
"Highest # of likes to lowest","Highest # of likes to lowest"
"Lowest # likes to highest","Lowest # likes to highest"
"Highest # of comments to lowest","Highest # of comments to lowest"
"Lowest # of comments to highes","Lowest # of comments to highes"
"Random order","Random order"
Random,Random
"Instagram Widget","Instagram Widget"
"Instagram Widget Settings","Instagram Widget Settings"
Tokens,Tokens
"WeltPixel InstagramWidget","WeltPixel InstagramWidget"
"Instagram Api Type","Instagram Api Type"
"Instagram Client Id","Instagram Client Id"
"Instagram Access Token","Instagram Access Token"
Title,Title
Description,Description
"Container Background Color","Container Background Color"
"Open Images in new Tab","Open Images in new Tab"
"Images Per Row","Images Per Row"
"Images Should Have Padding","Images Should Have Padding"
"Add ALT text for image","Add ALT text for image"
"Custom ALT tag","Custom ALT tag"
"Display Image Caption","Display Image Caption"
"Enables displaying captions for each post as overlay on hover.","Enables displaying captions for each post as overlay on hover."
"Optimize Image Layout","Optimize Image Layout"
"Enable Lazy Image Load","Enable Lazy Image Load"
"You must have the <a target=""_blank"" href=""https://www.weltpixel.com/magento-2-lazy-load-product-extension.html"">LAZY LOAD PRODUCTS AND IMAGES</a> module for this functionality.","You must have the <a target=""_blank"" href=""https://www.weltpixel.com/magento-2-lazy-load-product-extension.html"">LAZY LOAD PRODUCTS AND IMAGES</a> module for this functionality."
"Show Videos","Show Videos"
"Content Cache Time","Content Cache Time"
"The time (in minutes) the content it will be cached in your browser. (Default is 60) ","The time (in minutes) the content it will be cached in your browser. (Default is 60) "
"Number of images","Number of images"
"Number of images to be pulled from Instagram and displayed. Default: 6","Number of images to be pulled from Instagram and displayed. Default: 6"
SortBy,SortBy
"Sort the images in a set order.","Sort the images in a set order."
Resolution,Resolution
"Size of the images to get.","Size of the images to get."
"Feed Type","Feed Type"
"Customize what Instafeed fetches. (Tag and Location was removed by Instagram)","Customize what Instafeed fetches. (Tag and Location was removed by Instagram)"
"User Id","User Id"
"Unique id of a user to get","Unique id of a user to get"
"Customize what Instafeed fetches.","Customize what Instafeed fetches."
"User Name","User Name"
"Username of a user to get","Username of a user to get"
"Tag Name","Tag Name"
"Images with a specific tag","Images with a specific tag"
"Use Auto Refreshed Token","Use Auto Refreshed Token"
Token,Token
"Insert Instagram token that does not auto refresh. This token expires and needs to be changed manually in the widget after expiration.","Insert Instagram token that does not auto refresh. This token expires and needs to be changed manually in the widget after expiration."
"Add your tokens in WeltPixel -> Instagram Widgets Settings.","Add your tokens in WeltPixel -> Instagram Widgets Settings."
