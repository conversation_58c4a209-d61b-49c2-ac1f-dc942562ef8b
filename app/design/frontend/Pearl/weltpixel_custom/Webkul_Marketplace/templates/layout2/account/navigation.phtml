<?php
/**
 * Webkul Software.
 *
 * @category  Webkul
 * @package   Webkul_Marketplace
 * <AUTHOR>
 * @copyright Copyright (c) Webkul Software Private Limited (https://webkul.com)
 * @license   https://store.webkul.com/license.html
 */
/***
 * @var $block \Webkul\Marketplace\Block\Account\Navigation
 * @var $escaper \Magento\Framework\Escaper
 */
?>
<?php
    $_helper = $block->getMpHelper();
    $isPartner= $_helper->isSeller();
    $magentoCurrentUrl = $block->getCurrentUrl();
    $allow_pro_type=$_helper->getAllowedProductType();
    $isSellerGroup = $_helper->isSellerGroupModuleInstalled();
    $productNotificationCount = $block->getProductNotificationCount();
    $orderNotificationCount = $block->getOrderNotificationCount();
    $transactionCount = $block->getTransactionNotificationCount();
    $sellerId = $_helper->getCustomerId();
    $customerArray = $block->getCustomerCollection($sellerId);
    $accConnectUrl =  $block->getUrl(
        'mpmultishopifystoremageconnect/account/connect',
        ['_secure' => $block->getRequest()->isSecure()]
    );
    $templateView = $block->getUrl(
        'mpmultishopifystoremageconnect/templates/view',
        ['_secure' => $block->getRequest()->isSecure()]
    );
    $priceRule = $block->getUrl(
        'mpmultishopifystoremageconnect/pricerule/view',
        ['_secure' => $block->getRequest()->isSecure()]
    );
    $pickupEnable = true;
?>
<nav data-mage-init='{"Webkul_Marketplace/js/sellerNavigation": {}}' class="wk-mp-menu wk-mp-main">
    <ul id="wk-mp-nav">
        <?php if ($isPartner): ?>
            <?php if ($_helper->isAllowed('marketplace/account/dashboard')): ?>
                <li class="wk-mp-item-dashboard level-0
                <?= /* @noEscape */ strpos($magentoCurrentUrl, 'marketplace/account/dashboard')
                ? "current active":"";?>" id="wk-mp-menu-dashboard" >
                    <a href="<?= $escaper->escapeUrl($block
                    ->getUrl('marketplace/account/dashboard', ['_secure' => $block
                    ->getRequest()->isSecure()])); ?>"
                    class="<?= /* @noEscape */ strpos($magentoCurrentUrl, 'marketplace/account/dashboard')
                    ? "active":"";?>">
                        <span><?= /* @noEscape */ __('Dashboard') ?></span>
                    </a>
                </li>
            <?php endif; ?>
            <?php if ($_helper->isAllowed('sportsclub/account/sportsclubrevenuedashboard')): ?>
                <?php
                     if (array_key_exists('wkv_club_unique_identfier', $customerArray[0])) {
                    ?>
                <li class="wk-mp-item-revenue-dashboard level-0
                <?= /* @noEscape */ strpos($magentoCurrentUrl, 'sportsclub/account/sportsclubrevenuedashboard')
                ? "current active":"";?>" id="wk-mp-menu-revenue-dashboard" >
                    <a href="<?= $escaper->escapeUrl($block
                    ->getUrl('sportsclub/account/sportsclubrevenuedashboard', ['_secure' => $block
                    ->getRequest()->isSecure()])); ?>"
                    class="<?= /* @noEscape */ strpos($magentoCurrentUrl, 'sportsclub/account/sportsclubrevenuedashboard')
                    ? "active":"";?>">
                        <span><?= /* @noEscape */ __('Revenue') ?></span>
                    </a>
                </li>
                <?php
                    }
                ?>
            <?php endif; ?>
            <?php if ($_helper->isAllowed('marketplace/order/history')): ?>
                <li class="nav item notification-link wk-mp-item-order level-0
                <?= /* @noEscape */ strpos($magentoCurrentUrl, 'marketplace/order')
                && (!strpos($magentoCurrentUrl, 'marketplace/order/shipping/'))
                ? "current active":"";?>" id="wk-mp-menu-order" >
                    <a href="<?= $escaper->escapeUrl($block
                    ->getUrl('marketplace/order/history', ['_secure' => $block
                    ->getRequest()->isSecure()])); ?>"
                    class="<?= /* @noEscape */ strpos($magentoCurrentUrl, 'marketplace/order')
                     && (!strpos($magentoCurrentUrl, 'marketplace/order/shipping/'))? "active":"";?>">
                        <span><?= /* @noEscape */ __('Orders') ?></span>
                    </a>
                </li>
            <?php endif; ?>
            <?php
                $actions = ["marketplace/product/add", "marketplace/product/productlist",
                "marketplace/product_attribute/new"];
                ?>
            <?php if ($_helper->isAllowed($actions)): ?>
                <li class="wk-mp-item-product level-0 <?= /* @noEscape */
                strpos($magentoCurrentUrl, 'marketplace/product/')
                || strpos($magentoCurrentUrl, 'mpsellercategory/category/manage')
                || strpos($magentoCurrentUrl, 'mpmultishopifystoremageconnect')
                || strpos($magentoCurrentUrl, 'mpmultishopifystoremageconnect/templates/view')
                || strpos($magentoCurrentUrl, 'mpmultishopifystoremageconnect/pricerule/view')
                    ? "current active":"";?>"
                 id="wk-mp-menu-product">
                    <a href="#" onclick="return false;" class="">
                        <span><?= /* @noEscape */ __('Products')?></span>
                    </a>
                    <div class="wk-mp-submenu">
                        <strong class="wk-mp-submenu-title"><?= /* @noEscape */ __('Products')?></strong>
                        <a href="#" class="action-close _close" data-role="wk-mp-close-submenu"></a>
                        <ul>
                            <li data-ui-id="menu-webkul-marketplace-menu" class="item-menu parent level-1">
                                <strong class="wk-mp-submenu-group-title">
                                    <span><?= /* @noEscape */ __('Menu')?></span>
                                </strong>
                                <div class="wk-mp-submenu">
                                    <ul>
                                        <?php if ($_helper->isAllowed('marketplace/product/add')): ?>
                                            <li class="level-2">
                                                <a href="<?= $escaper->escapeUrl($block
                                                ->getUrl('marketplace/product/add', ['_secure' => $block
                                                ->getRequest()->isSecure()])); ?>">
                                                    <span><?= /* @noEscape */ __('Add New Product') ?></span>
                                                </a>
                                            </li>
                                        <?php endif; ?>
                                        <?php if ($_helper->isAllowed('marketplace/product/productlist')): ?>
                                            <li class="level-2">
                                                <a href="<?= $escaper->escapeUrl($block
                                                ->getUrl('marketplace/product/productlist', ['_secure' => $block
                                                ->getRequest()->isSecure()])); ?>">
                                                    <span><?= /* @noEscape */ __('Product List') ?></span>
                                                </a>
                                            </li>
                                        <?php endif; ?>
                                        <?php if ($isPartner): ?>
                                            <?php if (($isSellerGroup &&
                                                    $_helper->isAllowedAction('mpsellercategory/category/')) || !$isSellerGroup): ?>
                                                <li class="level-2">
                                                    <a
                                                        href=
                                                        "<?=  $escaper->escapeUrl($block->getUrl('mpsellercategory/category/manage', ['_secure' => 1])); ?>"
                                                        class=
                                                        "<?=  $escaper->escapeUrl(strpos($magentoCurrentUrl, 'mpsellercategory/category/')? "active":"");?>">
                                                        <?=  $escaper->escapeHtml(__('Manage Categories')) ?>
                                                    </a>
                                                </li>
                                            <?php endif; ?>
                                        <?php endif; ?>
                                        <strong class="wk-mp-submenu-group-title">
                                            <span><?= /* @noEscape */ __('Connect Shopify')?></span>
                                        </strong>
                                    <?php if ($pickupEnable && $isPartner): ?>
                                        <?php if (($isSellerGroup &&
                                                $_helper->isAllowedAction('mpmultishopifystoremageconnect/account/connect')) || !$isSellerGroup): ?>
                                            <li class="level-2">
                                                <a
                                                    href="<?=/* @noEscape */ $accConnectUrl ?>">
                                                    <?=  $escaper->escapeHtml(__('Connect Account')) ?>
                                                </a>
                                            </li>
                                        <?php endif; ?>
                                        <?php if (($isSellerGroup &&
                                                $_helper->isAllowedAction('mpmultishopifystoremageconnect/templates/view')) || !$isSellerGroup): ?>
                                            <li class="level-2">
                                                <a
                                                    href="<?=/* @noEscape */ $templateView ?>">
                                                    <?=  $escaper->escapeHtml(__('Listing Template')) ?>
                                                </a>
                                            </li>
                                        <?php endif; ?>
                                        <?php if (($isSellerGroup &&
                                                $_helper->isAllowedAction('mpmultishopifystoremageconnect/pricerule/view')) || !$isSellerGroup): ?>
                                            <li class="level-2">
                                                <a
                                                    href="<?=/* @noEscape */ $priceRule ?>">
                                                    <?=  $escaper->escapeHtml(__('Price Rules')) ?>
                                                </a>
                                            </li>
                                        <?php endif; ?>
                                    <?php endif; ?>
                                        <?php if ($_helper->isAllowed('marketplace/product_attribute/new') && !$_helper->getConfigValue('product_settings', 'disable_seller_create_attribute')): ?>
                                            <?php if (strpos($allow_pro_type, 'configurable') !== false): ?>
                                                <li class="level-2">
                                                    <a href="<?= $escaper->escapeUrl($block
                                                    ->getUrl('marketplace/product_attribute/new', ['_secure' => $block
                                                    ->getRequest()->isSecure()])); ?>">
                                                        <span><?= /* @noEscape */ __('Configurable Attribute') ?></span>
                                                    </a>
                                                </li>
                                            <?php endif; ?>
                                        <?php endif; ?>
                                    </ul>
                                </div>
                            </li>
                        </ul>
                    </div>
                </li>
            <?php endif; ?>
            <?php if ($_helper->isAllowed('marketplace/account/customer')): ?>
                <?php if ($_helper->getSellerProfileDisplayFlag()): ?>
                    <li class="nav item notification-link wk-mp-item-customer level-0
                    <?= /* @noEscape */ strpos($magentoCurrentUrl, 'marketplace/account/customer')
                    ? "current active":"";?>" id="wk-mp-menu-customer" >
                        <a href="<?= $escaper->escapeUrl($block
                        ->getUrl('marketplace/account/customer/', ['_secure' => $block->getRequest()->isSecure()])); ?>"
                        class="<?= /* @noEscape */ strpos($magentoCurrentUrl, 'marketplace/account/customer')
                        ? "active":"";?>">
                            <span><?= /* @noEscape */ __('Customers') ?></span>
                        </a>
                    </li>
                <?php endif; ?>
            <?php endif; ?>
            <?php if (
                $_helper->isAllowed('marketplace/transaction/history') ||
                $_helper->isAllowed('marketplace/account/earning') ||
                $_helper->isAllowed('seller_payouts/seller')
            ): ?>
                <li class="wk-mp-item-transaction level-0 <?= (strpos($magentoCurrentUrl, 'transaction') !== false || strpos($magentoCurrentUrl, 'earning') !== false || strpos($magentoCurrentUrl, 'stripeconnect') !== false) ? 'current active' : '' ?>" id="wk-mp-menu-finance">
                    <a href="#" onclick="return false;" class="">
                        <span><?= /* @noEscape */ __('Finance') ?></span>
                    </a>
                    <div class="wk-mp-submenu">
                        <strong class="wk-mp-submenu-title"><?= /* @noEscape */ __('Finance') ?></strong>
                        <a href="#" class="action-close _close" data-role="wk-mp-close-submenu"></a>
                        <ul>
                            <li class="level-1">
                                <a href="<?= $escaper->escapeUrl($block->getUrl('seller_payouts/seller/payouts', ['_secure' => $block->getRequest()->isSecure()])); ?>">
                                    <span><?= /* @noEscape */ __('Stripe Connect') ?></span>
                                </a>
                            </li>
                            <li class="level-1">
                                <a href="<?= $escaper->escapeUrl($block->getUrl('marketplace/transaction/history', ['_secure' => $block->getRequest()->isSecure()])); ?>">
                                    <span><?= /* @noEscape */ __('Transactions') ?></span>
                                </a>
                            </li>
                            <li class="level-1">
                                <a href="<?= $escaper->escapeUrl($block->getUrl('marketplace/account/earning', ['_secure' => $block->getRequest()->isSecure()])); ?>">
                                    <span><?= /* @noEscape */ __('Earnings') ?></span>
                                </a>
                            </li>
                        </ul>
                    </div>
                </li>
            <?php endif; ?>
            <?php /* if ($_helper->isAllowed('marketplace/order/shipping')): ?>
                <?php if ($_helper->getIsOrderManage()=="1"): ?>
                    <li class="wk-mp-item-order-shipping level-0 <?= /* @noEscape */ /*
                     strpos($magentoCurrentUrl, 'marketplace/order/shipping')
                     ? "current active":"";?>" id="wk-mp-menu-order-shipping" >
                        <a href="<?= $escaper->escapeUrl($block
                        ->getUrl('marketplace/order/shipping', ['_secure' => $block
                        ->getRequest()->isSecure()])); ?>" class="<?= /* @noEscape */ /*
                        strpos($magentoCurrentUrl, 'marketplace/order/shipping')? "active":"";?>">
                            <span><?= /* @noEscape */ /* __('Manage Print PDF Header Info') ?></span>
                        </a>
                    </li>
                <?php endif; ?>
            <?php endif; */ ?>
            <?php if ($_helper->isAllowed('marketplace/account/review')): ?>
                <?php if ($_helper->getSellerProfileDisplayFlag()): ?>
                    <li class="wk-mp-item-review level-0 <?= /* @noEscape */
                    strpos($magentoCurrentUrl, 'marketplace/account/review')
                    ? "current active":"";?>" id="wk-mp-menu-review" >
                        <a href="<?= $escaper->escapeUrl($block
                        ->getUrl('marketplace/account/review', ['_secure' => $block
                        ->getRequest()->isSecure()])); ?>" class="<?= /* @noEscape */
                        strpos($magentoCurrentUrl, 'marketplace/account/review')? "active":"";?>">
                            <span><?= /* @noEscape */ __('Review') ?></span>
                        </a>
                    </li>
                <?php endif; ?>
            <?php endif; ?>

            <?php // Disabled due to unfinished functionality ?>
            <?php if (0 && $_helper->getSellerProfileDisplayFlag()): ?>
                    <li class="nav item wk-seller level-0
                    <?= /* @noEscape */ strpos($magentoCurrentUrl, 'sellerrequest/seller/allrequest')
                    ? "current active":"";?>" id="wk-mp-seller-request" >
                        <a href="<?= $escaper->escapeUrl($block
                        ->getUrl('sellerrequest/seller/allrequest/', ['_secure' => $block->getRequest()->isSecure()])); ?>"
                        class="<?= /* @noEscape */ strpos($magentoCurrentUrl, 'sellerrequest/seller/allrequest')
                        ? "active":"";?>">
                            <span><?= /* @noEscape */ __('My Requests') ?></span>
                        </a>
                    </li>
            <?php endif; ?>

            <?= $block->getChildHtml('layout2_seller_account_navigation');?>
            <?= $block->getChildHtml('layout2_seller_account_navigation_settings_menu'); ?>
        <?php else: ?>
            <?= $block->getChildHtml('layout2_seller_account_navigation');?>
            <li class="wk-mp-item-order-shipping level-0 <?= /* @noEscape */
            strpos($magentoCurrentUrl, 'marketplace/account/becomeseller')
            ? "current active":"";?>" id="wk-mp-menu-becomeseller" >
                <a href="<?= $escaper->escapeUrl($block
                ->getUrl('marketplace/account/becomeseller/', ['_secure' => $block
                ->getRequest()->isSecure()])); ?>" class="<?= /* @noEscape */
                strpos($magentoCurrentUrl, 'marketplace/account/becomeseller')? "active":"";?>">
                    <span><?= /* @noEscape */ __('Become Seller') ?></span>
                </a>
            </li>
        <?php endif; ?>
    </ul>
</nav>
