#!/bin/bash

# Exit immediately if any command fails
set -e

# Log all commands to stdout for debugging
set -x

# Configuration
php="/usr/bin/php -dmemory_limit=-1 -dzend.enable_gc=0"
composer="${php:?} /usr/local/bin/composer"
app_path="/var/www/app"
timestamp="${TIMESTAMP}"
release_dir="${app_path}/releases/${timestamp}"
shared_dir="/mnt"
current_dir="${app_path}/current"
BUILD_DIR="./build"
environment="${ENVIRONMENT}"

# Create release directory
echo ""
echo ""
echo "-----------CREATE RELEASE DIRECTORY-----------"
echo ""
mkdir -p "${release_dir}"

# Move into the release directory
cd "${release_dir}" || exit

echo "sync media from repo folder and remove it."
rsync -aHvr "${release_dir}/pub/media/" /mnt/media/
rm -rf "${release_dir}/pub/media"
echo "update media symlink"
ln -s "${shared_dir}/media" "${release_dir}/pub/media"
echo ""
echo ""
echo "--------------GENERATE ENV FILE---------------"
echo ""

# Generate env.php from template
env_template="$BUILD_DIR/env.php.$environment.template"
if [ ! -f "$env_template" ]; then
    env_template="$BUILD_DIR/env.php.template"
fi
if [ ! -f "$env_template" ]; then
    echo "FATAL ERROR: Template file $env_template not found!"
    exit 1
fi
echo "Generate env.php file using $env_template environment template file"

env_file="./app/etc/env.php"
# Replace placeholders with environment variables
sed -e "s|{{REDIS_HOST}}|$MAGENTO_REDIS_HOST|g" \
    -e "s|{{REDIS_PORT}}|$MAGENTO_REDIS_PORT|g" \
    -e "s|{{GRAPHQL_SALT}}|$MAGENTO_GRAPHQL_SALT|g" \
    -e "s|{{FE_NAME}}|$MAGENTO_FE_NAME|g" \
    -e "s|{{AMQP_HOST}}|$MAGENTO_AMQP_HOST|g" \
    -e "s|{{AMQP_PORT}}|$MAGENTO_AMQP_PORT|g" \
    -e "s|{{AMQP_USER}}|$MAGENTO_AMQP_USER|g" \
    -e "s|{{AMQP_PASSWORD}}|$MAGENTO_AMQP_PASSWORD|g" \
    -e "s|{{AMQP_VHOST}}|$MAGENTO_AMQP_VHOST|g" \
    -e "s|{{DB_HOST}}|$MAGENTO_DB_HOST|g" \
    -e "s|{{DB_NAME}}|$MAGENTO_DB_NAME|g" \
    -e "s|{{DB_USERNAME}}|$MAGENTO_DB_USERNAME|g" \
    -e "s|{{DB_PASSWORD}}|$MAGENTO_DB_PASSWORD|g" \
    -e "s|{{CRYPT_KEY}}|$MAGENTO_CRYPT_KEY|g" \
    -e "s|{{REDIS_SESSION_HOST}}|$MAGENTO_REDIS_SESSION_HOST|g" \
    -e "s|{{REDIS_SESSION_PORT}}|$MAGENTO_REDIS_SESSION_PORT|g" \
    -e "s|{{REDIS_SESSION_DB}}|$MAGENTO_REDIS_SESSION_DB|g" \
    -e "s|{{DOWNLOADABLE_DOMAIN}}|$MAGENTO_DOWNLOADABLE_DOMAIN|g" \
    -e "s|{{OPENSEARCH_HOSTNAME}}|$MAGENTO_OPENSEARCH_HOSTNAME|g" \
    -e "s|{{OPENSEARCH_PORT}}|$MAGENTO_OPENSEARCH_PORT|g" \
    -e "s|{{OPENSEARCH_INDEX_PREFIX}}|$MAGENTO_OPENSEARCH_INDEX_PREFIX|g" \
    -e "s|{{OPENSEARCH_AUTH}}|$MAGENTO_OPENSEARCH_AUTH|g" \
    -e "s|{{OPENSEARCH_USERNAME}}|$MAGENTO_OPENSEARCH_USERNAME|g" \
    -e "s|{{OPENSEARCH_PASSWORD}}|$MAGENTO_OPENSEARCH_PASSWORD|g" \
    "$env_template" > "$env_file"

# Install dependencies
echo ""
echo ""
echo "-----------INSTALL DEPENDENCIES-----------"
echo ""

export COMPOSER_ALLOW_SUPERUSER=1
$composer install -n --no-dev --prefer-dist
#./vendor/bin/ece-patches apply -vvv # @not needed in onpremise setup

# Run Magento commands (only on the main node)
echo ""
echo ""
echo "-----------EXECUTE bin/magento setup:upgrade-----------"
echo ""

$php bin/magento setup:upgrade --no-interaction -vvv

echo ""
echo ""
echo "-----------EXECUTE bin/magento setup:di:compile-----------"
echo ""

$php bin/magento setup:di:compile -vvv

echo ""
echo ""
echo "-----------EXECUTE bin/magento setup:static-content:deploy-----------"
echo ""

$php bin/magento setup:static-content:deploy -f -j 5 --no-ansi --no-interaction

echo ""
echo ""
echo "----------------- UPDATE PRIVILEGES -----------------"
echo ""

chown www-data: ${release_dir}/ -R
echo ""
echo ""
echo "-----------CREATE RELEASE SYMLINK-----------"
echo ""
# Update the symlink to the new release
ln -sfn "${release_dir}" "${current_dir}"

# Clear opcache and reload PHP-FPM + Nginx
$php -r 'opcache_reset();'
sudo systemctl reload php8.3-fpm
sudo systemctl reload nginx

# Clean up old releases (keep the last 5 releases)
rm -rf /var/www/app/current/aws-deploy.sh
cd "${app_path}/releases" && ls -t | tail -n +6 | xargs rm -rf
